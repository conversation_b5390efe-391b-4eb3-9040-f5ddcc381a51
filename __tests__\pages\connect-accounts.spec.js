import "reflect-metadata";
import { act, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ConnectAccounts from "./../../pages/connect-accounts";
import { aConnectedChannel } from "../factories/creators/ConnectedChannels";
import { INVALID_TIKTOK_SCOPE, onToastClose, WINDOW_PARAMS } from "../../utils";
import { aConnectedAccount } from "../factories/creators/ConnectedAccounts";
import { mockMatchMedia, mockWindowReload, restoreWindowReload } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import { useRouter } from "next/router";
import { useAppContext } from "../../src/context";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import CreatorsService, { CreatorWithPayableStatusProfile } from "../../src/api/services/CreatorsService";
import ConnectedAccountsService from "../../src/api/services/ConnectedAccountsService";
import { useDependency } from "../../src/context/DependencyContext";
import { axe } from "jest-axe";

jest.mock("../../src/api/services/ConnectedAccountsService");
jest.mock("../../src/api/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../src/api/services/CreatorsService"),
    getCreatorWithPayableStatus: jest.fn()
  };
});
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  useIsMounted: jest.fn().mockImplementation(() => jest.fn().mockReturnValue(true)),
  onToastClose: jest.fn()
}));
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../analytics/browser/src/ampli", () => ({
  ampli: {
    identify: jest.fn(),
    receivedErrorMessage: jest.fn()
  }
}));
jest.mock("../../src/context/DependencyContext");

describe("ConnectAccounts", () => {
  mockMatchMedia();
  const analytics = {
    confirmedSocialMediaChannel: jest.fn(),
    canceledOnboardingFlow: jest.fn()
  };
  const REMOVE_ONLY_ACCOUNT = "disconnect-account-conflicting-action";
  const YOUTUBE_NO_CHANNEL_ERROR = "save-you-tube-account-unknown-connected-account";
  const INSTA_WARNING_ERROR = "save-instagram-account-unknown-instagram-business-account";
  const INSTA_CANNOT_CONNECT = "save-instagram-account-cannot-connect-account";
  const router = { pathname: "/", push: jest.fn(), locale: "en-us" };
  let creatorWith3Accounts, creatorWith3ExpiredAccounts;
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/onboarding/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  const mockUseDetectScreen = jest.fn();
  const dispatch = jest.fn();
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    creatorWith3Accounts = new CreatorWithPayableStatusProfile(aCreatorWithPayableStatus());
    creatorWith3ExpiredAccounts = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        connectedAccounts: [
          aConnectedAccount({ type: "TWITCH" }),
          aConnectedAccount({ type: "FACEBOOK" }),
          aConnectedAccount({ type: "YOUTUBE" })
        ]
      })
    );
    useAppContext.mockReturnValue({
      dispatch,
      state: {
        onboardingSteps: steps
      }
    });
    useDependency.mockReturnValue({
      analytics,
      errorHandler,
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
  });

  it("shows toast message when a Google account doesn't have a YouTube channel", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    const { unmount } = renderPage(<ConnectAccounts {...{ error: { code: YOUTUBE_NO_CHANNEL_ERROR } }} />);

    await waitFor(() => {
      // Creator has 3 connected accounts
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[2].username)).toBeInTheDocument();
      // Toast error message
      expect(screen.getByRole("alert")).toBeInTheDocument();
      expect(screen.getByText(/unhandledError/)).toBeInTheDocument();
      // Message for missing YouTube channel
      expect(screen.getByText(/connect-accounts:messages:youtubeNoChannelError/)).toBeInTheDocument();
    });
    unmount(); // remove toast
  });

  it("shows only one toast for insta warning", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    const { unmount } = renderPage(<ConnectAccounts {...{ error: { code: INSTA_WARNING_ERROR } }} />);

    await waitFor(() => {
      expect(screen.getAllByRole("alert")).toHaveLength(1);
      unmount(); // remove toast
    });
  });

  it("shows toast message when a connected Instagram account is not connected to a Facebook page", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    const { unmount } = renderPage(<ConnectAccounts {...{ error: { code: INSTA_WARNING_ERROR } }} />);

    await waitFor(() => {
      // Creator has 3 connected accounts
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[2].username)).toBeInTheDocument();
      // Toast warning message
      expect(screen.getByRole("alert")).toBeInTheDocument();
      expect(screen.getByText(/actionTitle/)).toBeInTheDocument();
      // Message and link to connect Instagram business account to a Facebook page
      expect(screen.getByText(/connect-accounts:messages:actionDescription1/)).toBeInTheDocument();
      expect(screen.getByText(/connect-accounts:messages:actionDescription3/)).toBeInTheDocument();
    });
    unmount(); // remove toast
  });

  it("displays connected accounts information.", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    renderPage(<ConnectAccounts />);

    // Page labels
    expect(await screen.findByRole("heading", { name: /connect-accounts:title/i })).toBeInTheDocument();
    expect(await screen.findByText("connect-accounts:message")).toBeInTheDocument();
    expect(await screen.findByText("connect-accounts:subTitle")).toBeInTheDocument();
    expect(await screen.findByText("connect-accounts:myAccount")).toBeInTheDocument();
    expect(await screen.findAllByText("connect-accounts:addAccount")).toHaveLength(5);
    expect(await screen.findAllByText("connect-accounts:removeAccount")).toHaveLength(3);
    expect(await screen.findByText("connect-accounts:message")).toBeInTheDocument();
    // Creator has 3 connected accounts
    expect(screen.getByText(creatorWith3Accounts.connectedAccounts[0].username)).toBeInTheDocument();
    expect(screen.getByText(creatorWith3Accounts.connectedAccounts[1].username)).toBeInTheDocument();
    expect(screen.getByText(creatorWith3Accounts.connectedAccounts[2].username)).toBeInTheDocument();
  });

  it("it shows button to connect a TikTok account if feature is enabled", async () => {
    creatorWith3Accounts = aCreatorWithPayableStatus({
      connectedAccounts: [aConnectedAccount({ type: "FACEBOOK" }, { type: "INSTAGRAM" }, { type: "YOUTUBE" })]
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    renderPage(<ConnectAccounts />);

    expect(await screen.findAllByText("connect-accounts:addAccount")).toHaveLength(5);
    expect(await screen.findByText(/TikTok/i)).toBeInTheDocument();
  });

  it("closes remove account dialogue on cancel", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });
    renderPage(<ConnectAccounts />);
    const removeAccountButton = await waitFor(async () => {
      const removeAccountButtons = screen.queryAllByText(/connect-accounts:removeAccount/i);
      expect(removeAccountButtons.length).toBeGreaterThan(0);
      return removeAccountButtons[0];
    });
    await userEvent.click(removeAccountButton);
    const modal = await screen.findByRole("dialog");
    const { getByText } = within(modal);
    const cancelButton = getByText("cancel");

    await userEvent.click(cancelButton);

    await waitFor(() => expect(screen.queryByText(/^remove$/i)).not.toBeInTheDocument());
  });

  it("prevents removing a connected account, if it's the only one", async () => {
    const accountId = "e2ad0961-cdbc-444e-885c-8cbb9b8cbf07";
    const errorResponse = {
      message:
        "Cannot disconnect channel because: Deleting channel not allowed. Creator should have at least one channel.",
      code: REMOVE_ONLY_ACCOUNT
    };
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        domainError: {
          response: {
            data: {
              code: REMOVE_ONLY_ACCOUNT
            }
          }
        },
        onboardingSteps: steps
      }
    });
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        connectedAccounts: [aConnectedChannel({ id: accountId })]
      })
    );
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    ConnectedAccountsService.removeConnectedAccount.mockRejectedValue({
      response: {
        data: errorResponse
      }
    });
    const { unmount } = renderPage(<ConnectAccounts />);
    await userEvent.click(await screen.findByText("connect-accounts:removeAccount"));

    await userEvent.click(await screen.findByRole("button", { name: /^remove$/i }));

    expect(await screen.findByRole("alert")).toBeInTheDocument();
    expect(await screen.findByText(/unhandledError/)).toBeInTheDocument();
    // Must have at least one account message
    expect(await screen.findByText(/connect-accounts:messages:removeAccountDescription/)).toBeInTheDocument();
    unmount(); // remove toast
  });

  it("shows modal with logout option, when clicking on close icon", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    renderPage(<ConnectAccounts />);

    await userEvent.click(screen.getByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("shows toast message when another Instagram account is connected", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    const { unmount } = renderPage(<ConnectAccounts {...{ error: { code: INSTA_CANNOT_CONNECT } }} />);

    await waitFor(() => {
      // Creator has 3 connected accounts
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[2].username)).toBeInTheDocument();
      // Toast error message
      expect(screen.getByRole("alert")).toBeInTheDocument();
      expect(screen.getByText(/cannotConnectInstaAccountHeader$/)).toBeInTheDocument();
      // Message for Instagram error
      expect(screen.getByText(/^connect-accounts:messages:cannotConnectInstaAccount$/)).toBeInTheDocument();
    });
    unmount(); // remove toast
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });
    renderPage(<ConnectAccounts />);
    await userEvent.click(screen.getByRole("button", { name: /closeHeader/i }));
    await userEvent.click(await screen.findByRole("button", { name: /yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Connected Accounts' when click on 'Next' button in the Connected Accounts page", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });
    renderPage(<ConnectAccounts />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      // TODO: Add `toHaveBeenCalledWith`, currently it's not possible because of too many renderings
      expect(analytics.confirmedSocialMediaChannel).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith("/communication-preferences");
    });
  });

  it("reconnects an account with an expired access token when clicking on re-connect button", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    ConnectedAccountsService.clearAccountType.mockImplementation(() => Promise.resolve());

    const accountType = creatorWith3ExpiredAccounts.connectedAccounts[0].type;
    renderPage(<ConnectAccounts {...{ myProfileView: true }} />);
    let reconnectButtons;
    await waitFor(() => {
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[2].username)).toBeInTheDocument();
      reconnectButtons = screen.getAllByRole("button", { name: "connect-accounts:reconnectAccount" });
    });

    await userEvent.click(reconnectButtons[0]);

    await waitFor(() => {
      expect(window.open).toHaveBeenCalledTimes(1);
      expect(window.open).toHaveBeenCalledWith(`/api/${accountType.toLowerCase()}-login`, "_blank", WINDOW_PARAMS);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledWith();
    });
    restoreWindowReload();
  });

  it("shows error page when clearing account type from session throws 404 error", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    const accountType = creatorWith3ExpiredAccounts.connectedAccounts[0].type;
    ConnectedAccountsService.clearAccountType.mockImplementation(() =>
      Promise.reject(new Error("Something went wrong"))
    );
    renderPage(<ConnectAccounts {...{ myProfileView: true }} />);
    let reconnectButtons;
    await waitFor(() => {
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[2].username)).toBeInTheDocument();
      reconnectButtons = screen.getAllByRole("button", { name: "connect-accounts:reconnectAccount" });
    });

    await userEvent.click(reconnectButtons[0]);

    await waitFor(() => {
      expect(window.open).toHaveBeenCalledTimes(1);
      expect(window.open).toHaveBeenCalledWith(`/api/${accountType.toLowerCase()}-login`, "_blank", WINDOW_PARAMS);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
      expect(window.location.reload).not.toHaveBeenCalled();
    });
    restoreWindowReload();
  });

  it("shows error toast when clearing account type throws 409 error", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    const accountType = creatorWith3ExpiredAccounts.connectedAccounts[0].type;
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        domainError: {
          response: {
            data: {
              code: 409
            }
          }
        },
        onboardingSteps: steps
      }
    });
    ConnectedAccountsService.clearAccountType.mockImplementation(() => Promise.reject());
    const { unmount } = renderWithToast(<ConnectAccounts {...{ myProfileView: true }} />);
    let reconnectButtons;
    await waitFor(() => {
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3ExpiredAccounts.connectedAccounts[2].username)).toBeInTheDocument();
      reconnectButtons = screen.getAllByRole("button", { name: "connect-accounts:reconnectAccount" });
    });

    await userEvent.click(reconnectButtons[0]);

    await waitFor(() => {
      expect(window.open).toHaveBeenCalledTimes(1);
      expect(window.open).toHaveBeenCalledWith(`/api/${accountType.toLowerCase()}-login`, "_blank", WINDOW_PARAMS);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      const toastContainer = screen.getByRole("alert");
      const { getByRole } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent("unhandledError");
      expect(getByRole("button", { name: /close/i })).toBeInTheDocument();
      expect(window.location.reload).not.toHaveBeenCalled();
    });
    restoreWindowReload();
    unmount();
  });

  it("shows a Loader component", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        isLoading: true
      }
    });

    renderPage(<ConnectAccounts {...{ myProfileView: true }} />);

    // connect account is a subpage of profile(loads both loader from profile & connec accounts pages)
    expect(await screen.findAllByAltText(/Loading/i)).toHaveLength(2);
    // Wait for the connected accounts to be shown, after the loading icon is hidden
    const twitchLabels = await screen.findAllByText(/twitch/i);
    expect(twitchLabels.length).toBeGreaterThan(0);
  });

  it("expects the connect button to be disabled, when no channel is selected", async () => {
    window.open = jest.fn().mockReturnValue({ closed: true });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    const { baseElement } = renderPage(
      <ConnectAccounts {...{ myProfileView: true, pages: [{ id: "dshfdgh23435" }] }} />
    );

    await waitFor(() => {
      const { getByRole, getByText } = within(baseElement);
      const channelRadioButton = getByRole("radio", { checked: false });
      const connectButton = getByText("connect");
      expect(channelRadioButton).toBeInTheDocument();
      expect(connectButton).toBeDisabled();
    });
  });

  it("clears facebook pages in session on closing the modal", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const selectedPage = [{ id: "shjsdjf", accessToken: "terfhj", name: "hari70aTest" }];
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    ConnectedAccountsService.clearFbPages.mockImplementation(() => Promise.resolve());

    renderPage(<ConnectAccounts {...{ myProfileView: true, pages: selectedPage }} />);

    await userEvent.click(await screen.findByRole("button", { name: "cancel" }));

    await waitFor(() => {
      expect(ConnectedAccountsService.clearFbPages).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledWith();
    });
    restoreWindowReload();
  });

  it("clear the account type in session after selecting a Facebook page", async () => {
    mockWindowReload();
    window.open = jest.fn().mockReturnValue({ closed: true });
    const creatorId = creatorWith3ExpiredAccounts.id;
    const selectedPage = [{ id: "shjsdjf", accessToken: "terfhj", name: "hari70aTest" }];
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    ConnectedAccountsService.connectFbPages.mockImplementation(() => Promise.resolve());
    ConnectedAccountsService.clearAccountType.mockImplementation(() => Promise.resolve());

    const { baseElement } = renderPage(<ConnectAccounts {...{ pages: selectedPage, user: { id: creatorId } }} />);

    const { getByRole } = within(baseElement);
    // Select a Facebook page
    await userEvent.click(await screen.findByRole("radio"));
    expect(getByRole("button", { name: "connect" })).toBeEnabled();

    await userEvent.click(getByRole("button", { name: "connect" }));

    await waitFor(() => {
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
      expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledTimes(1);
      expect(window.location.reload).toHaveBeenCalledWith();
    });
    restoreWindowReload();
  });

  it("removes a connected account in my profile page", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    ConnectedAccountsService.removeConnectedAccount.mockImplementation(() => Promise.resolve());

    renderPage(<ConnectAccounts {...{ myProfileView: true }} />);
    const removeAccounts = await screen.findAllByText("connect-accounts:removeAccount");
    await userEvent.click(removeAccounts[0]);

    await userEvent.click(await screen.findByText(/^remove$/i));

    await waitFor(() => {
      expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledTimes(1);
    });
  });

  it("removes a connected account in registration flow", async () => {
    const router = {
      pathname: "/connected-accounts",
      route: "/connected-accounts",
      push: jest.fn()
    };
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    ConnectedAccountsService.removeConnectedAccount.mockImplementation(() => Promise.resolve());

    renderPage(<ConnectAccounts {...{ myProfileView: false }} />);
    const removeAccounts = await screen.findAllByText("connect-accounts:removeAccount");
    await userEvent.click(removeAccounts[0]);

    await userEvent.click(await screen.findByText(/^remove$/i));

    await waitFor(() => {
      expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledTimes(1);
      /** On removing an account, the following happened.
       * 1.Event bubbling happens.
       * 2.Calls the form submit handler.
       * 3.Redirect to communication-preference page.
       *
       * Inorder to validate this, I added this assertion
       * */
      expect(router.push).not.toHaveBeenCalledWith("/communication-preferences");
    });
  });

  it("navigates to Creator type page when user click on 'Back' button", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });

    renderPage(<ConnectAccounts {...{ myProfileView: false }} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/creator-type"));
  });

  it("detects viewport size and shows page back button for mobile", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    renderPage(<ConnectAccounts {...{ myProfileView: false }} />);

    expect(await screen.findByRole("button", { name: /Back/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /Back/i })).toHaveClass("display-back-bt");
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    window.open = jest.fn().mockReturnValue({ closed: true });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage, userNavigated: true, onboardingSteps: steps }
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3ExpiredAccounts
    });
    const { unmount } = renderWithToast(
      <ConnectAccounts {...{ myProfileView: true, pages: [{ id: "dshfdgh23435" }] }} />
    );
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(getByRole("button", { name: /close/i }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch, "unhandledError");
    });
    unmount();
    jest.useRealTimers();
  });

  it("shows toast message when a 'TikTok' account doesn't have sufficient permissions", async () => {
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creatorWith3Accounts
    });

    const { unmount } = renderPage(
      <ConnectAccounts {...{ error: { code: INVALID_TIKTOK_SCOPE } }} invalidTikTokScope={true} />
    );

    await waitFor(() => {
      // Creator has 3 connected accounts
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[0].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[1].username)).toBeInTheDocument();
      expect(screen.getByText(creatorWith3Accounts.connectedAccounts[2].username)).toBeInTheDocument();
      // Toast error message
      expect(screen.getByRole("alert")).toBeInTheDocument();
      const { getByRole, getAllByText } = within(screen.getByRole("alert"));
      expect(getByRole("heading")).toHaveTextContent("unhandledError");
      // Since the toast's title and body are with same message "Oops something went wrong", we checked the length here
      expect(getAllByText(/unhandledError/)).toHaveLength(2);
    });
    unmount(); // remove toast
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(<ConnectAccounts {...{ myProfileView: true, pages: [{ id: "dshfdgh23435" }] }} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
