import { Ampli } from "../../../analytics/browser/src/ampli";
import BrowserAnalytics, { AuthenticatedUserFactory } from "../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../factories/opportunities/OpportunityWithPerks";
import { CreatorWithExpiredAccountsProfile, CreatorWithPayableStatusProfile } from "@src/api/services/CreatorsService";
import { aCreatorWithPayableStatus } from "../../factories/creators/CreatorWithPayableStatus";
import { aPrimaryFranchise, aSecondaryFranchise } from "../../factories/franchises/PreferredFranchise";
import { aPrimaryPlatform, aSecondaryPlatform } from "../../factories/platforms/PreferredPlatform";
import { aCreatorWithExpiredAccounts } from "../../factories/creators/CreatorWithExpiredAccount";
import { aConnectedAccount, aConnectedAccounts } from "../../factories/creators/ConnectedAccounts";
import { aUser } from "../../factories/User/User";
import { aPerk } from "@eait-playerexp-cn/metadata-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";

describe("BrowserAnalytics", () => {
  const locale = "en-us";
  const opportunity = new OpportunityWithDeliverables(
    aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      id: "c230bc75-faee-466e-a0e4-909be63427d7",
      title: "EA Play",
      gameTitle: "FIFA 2021",
      hasDeliverables: false,
      hasDiscordChannel: false,
      hasPayments: false,
      hasEvent: false,
      hasGameCodes: true,
      gameCodes: { availability: [{ platform: { label: "PS4" } }] },
      perks: [aPerk({ code: "TRAVEL" })]
    })
  );
  const user = AuthenticatedUserFactory.fromSession(aUser({ status: "ACTIVE" }));
  const initialInterestedCreator = anInitialInterestedCreator();
  const options = {
    user,
    program: "creator-network"
  };

  beforeEach(() => jest.clearAllMocks());

  it("logs 'Started Onboarding Flow' event", () => {
    const ampli = { identify: jest.fn(), startedOnboardingFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.startedOnboardingFlow({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.startedOnboardingFlow).toHaveBeenCalledTimes(1);
    expect(ampli.startedOnboardingFlow).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Canceled Onboarding Flow' event", () => {
    const ampli = { identify: jest.fn(), canceledOnboardingFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.canceledOnboardingFlow({ locale, page: "/onboarding/information" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
    expect(ampli.canceledOnboardingFlow).toHaveBeenCalledWith({
      "Page Abandoned": "/onboarding/information",
      Program: options.program
    });
  });

  it("logs 'Confirmed Franchise' event", () => {
    const ampli = { identify: jest.fn(), confirmedFranchise: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        preferredFranchises: [
          aPrimaryFranchise({ name: "FIFA" }),
          aSecondaryFranchise({ name: "FIFA" }),
          aSecondaryFranchise({ name: "Plants vs Zombies" })
        ]
      })
    );

    analytics.confirmedFranchise({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Primary Franchise": "FIFA",
      "Secondary Franchise": ["FIFA", "Plants vs Zombies"]
    });
    expect(ampli.confirmedFranchise).toHaveBeenCalledTimes(1);
    expect(ampli.confirmedFranchise).toHaveBeenCalledWith({
      "Primary Franchise": "FIFA",
      "Secondary Franchise": ["FIFA", "Plants vs Zombies"],
      Program: options.program
    });
  });

  it("logs 'Confirmed Creator Type' event", () => {
    const ampli = { identify: jest.fn(), confirmedCreatorType: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({ creatorTypes: ["PODCASTER", "YOUTUBER"] })
    );

    analytics.confirmedCreatorType({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Creator Types": ["PODCASTER", "YOUTUBER"]
    });
    expect(ampli.confirmedCreatorType).toHaveBeenCalledTimes(1);
    expect(ampli.confirmedCreatorType).toHaveBeenCalledWith({
      "Selected Creator Types": ["PODCASTER", "YOUTUBER"],
      Program: options.program
    });
  });

  it("logs 'Confirmed Social Media Channel' event", () => {
    const ampli = { identify: jest.fn(), confirmedSocialMediaChannel: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const creator = new CreatorWithExpiredAccountsProfile(
      aCreatorWithExpiredAccounts({
        connectedAccounts: aConnectedAccounts([
          aConnectedAccount({ type: "TWITCH" }),
          aConnectedAccount({ type: "FACEBOOK" }),
          aConnectedAccount({ type: "YOUTUBE" })
        ])
      })
    );

    analytics.confirmedSocialMediaChannel({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Connected Social Accounts": ["TWITCH", "FACEBOOK", "YOUTUBE"]
    });
    expect(ampli.confirmedSocialMediaChannel).toHaveBeenCalledTimes(1);
    expect(ampli.confirmedSocialMediaChannel).toHaveBeenCalledWith({
      "Connected Social Accounts": ["TWITCH", "FACEBOOK", "YOUTUBE"],
      Program: options.program
    });
  });

  it("logs 'Confirmed Platform' event", () => {
    const ampli = { identify: jest.fn(), confirmedPlatform: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const primaryPlatform = "Nintendo Switch";
    const secondaryPlatforms = ["PS4", "Xbox"];

    analytics.confirmedPlatform({
      locale,
      primaryPlatform,
      secondaryPlatforms
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Primary Platform": primaryPlatform,
      "Secondary Platform": secondaryPlatforms
    });
    expect(ampli.confirmedPlatform).toHaveBeenCalledTimes(1);
    expect(ampli.confirmedPlatform).toHaveBeenCalledWith({
      "Primary Platform": primaryPlatform,
      "Secondary Platform": secondaryPlatforms,
      Program: options.program
    });
  });

  it("logs 'Confirmed Communication Preferences' event", () => {
    const ampli = { identify: jest.fn(), confirmedCommunicationPreferences: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const contentLanguages = "English,Spanish";

    analytics.confirmedCommunicationPreferences({ locale, contentLanguages });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Content Languages": contentLanguages
    });
    expect(ampli.confirmedCommunicationPreferences).toHaveBeenCalledTimes(1);
    expect(ampli.confirmedCommunicationPreferences).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Signed Terms And Conditions' event", () => {
    const ampli = { identify: jest.fn(), signedTermsAndConditions: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const accepted = true;

    analytics.signedTermsAndConditions({ locale, accepted });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.signedTermsAndConditions).toHaveBeenCalledTimes(1);
    expect(ampli.signedTermsAndConditions).toHaveBeenCalledWith({
      "Agreed to T&C's": accepted,
      Program: options.program
    });
  });

  it("logs 'Completed Onboarding Flow' event", () => {
    const ampli = { identify: jest.fn(), completedOnboardingFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.completedOnboardingFlow({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.completedOnboardingFlow).toHaveBeenCalledTimes(1);
    expect(ampli.completedOnboardingFlow).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Clicked Footer Link' event", () => {
    const ampli = { identify: jest.fn(), clickedFooterLink: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const url = "/how-it-works";

    analytics.clickedFooterLink({ locale, url });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedFooterLink).toHaveBeenCalledTimes(1);
    expect(ampli.clickedFooterLink).toHaveBeenCalledWith({ "Link Clicked": url, Program: options.program });
  });

  it("logs 'Viewed Marketing Page' event", () => {
    const ampli = { identify: jest.fn(), viewedMarketingPage: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);
    const page = "/how-it-works";

    analytics.viewedMarketingPage({ locale, page });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.viewedMarketingPage).toHaveBeenCalledTimes(1);
    expect(ampli.viewedMarketingPage).toHaveBeenCalledWith({ "Page Displayed": page, Program: options.program });
  });

  it("logs 'Signed Out Of Creator Network' event", () => {
    const ampli = { identify: jest.fn(), signedOutOfCreatorNetwork: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.signedOutOfCreatorNetwork({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.signedOutOfCreatorNetwork).toHaveBeenCalledTimes(1);
    expect(ampli.signedOutOfCreatorNetwork).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Visited My Profile' event", () => {
    const ampli = { identify: jest.fn(), visitedMyProfile: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.visitedMyProfile({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.visitedMyProfile).toHaveBeenCalledTimes(1);
    expect(ampli.visitedMyProfile).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Updated Basic Information' event", () => {
    const ampli = { identify: jest.fn(), updatedBasicInformation: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedBasicInformation({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.updatedBasicInformation).toHaveBeenCalledTimes(1);
    expect(ampli.updatedBasicInformation).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Updated Creator Types In Profile' event", () => {
    const ampli = { identify: jest.fn(), updatedCreatorTypesInProfile: jest.fn() } as unknown as Ampli;
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({ creatorTypes: ["PODCASTER", "YOUTUBER"] })
    );
    const selectedCreatorTypes = ["PODCASTER", "PHOTOGRAPHER", "LIFESTYLE"];
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedCreatorTypesInProfile({ locale, creator, selectedCreatorTypes });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Creator Types": selectedCreatorTypes
    });
    expect(ampli.updatedCreatorTypesInProfile).toHaveBeenCalledTimes(1);
    expect(ampli.updatedCreatorTypesInProfile).toHaveBeenCalledWith({
      "Added Creator Types": ["PHOTOGRAPHER", "LIFESTYLE"],
      "Removed Creator Types": ["YOUTUBER"],
      Program: options.program
    });
  });

  it("logs 'Updated Primary Franchise' event", () => {
    const ampli = { identify: jest.fn(), updatedPrimaryFranchise: jest.fn() } as unknown as Ampli;
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({ preferredFranchises: [aPrimaryFranchise({ name: "FIFA" }), aSecondaryFranchise()] })
    );
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedPrimaryFranchise({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Primary Franchise": "FIFA"
    });
    expect(ampli.updatedPrimaryFranchise).toHaveBeenCalledTimes(1);
    expect(ampli.updatedPrimaryFranchise).toHaveBeenCalledWith({
      "Selected Franchise(s)": ["FIFA"],
      Program: options.program
    });
  });

  it("logs 'Updated Secondary Franchises' event", () => {
    const ampli = { identify: jest.fn(), updatedSecondaryFranchises: jest.fn() } as unknown as Ampli;
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        preferredFranchises: [
          aPrimaryFranchise(),
          aSecondaryFranchise({ name: "FIFA" }),
          aSecondaryFranchise({ name: "Plants vs Zombies" })
        ]
      })
    );
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedSecondaryFranchises({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Secondary Franchise": ["FIFA", "Plants vs Zombies"]
    });
    expect(ampli.updatedSecondaryFranchises).toHaveBeenCalledTimes(1);
    expect(ampli.updatedSecondaryFranchises).toHaveBeenCalledWith({
      "Selected Franchise(s)": ["FIFA", "Plants vs Zombies"],
      Program: options.program
    });
  });

  it("logs 'Updated Primary Platform In Profile' event", () => {
    const ampli = { identify: jest.fn(), updatedPrimaryPlatformInProfile: jest.fn() } as unknown as Ampli;
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        preferredPlatforms: [aPrimaryPlatform({ name: "Nintendo Switch" }), aSecondaryPlatform()]
      })
    );
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedPrimaryPlatformInProfile({ locale, creator });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Primary Platform": "Nintendo Switch"
    });
    expect(ampli.updatedPrimaryPlatformInProfile).toHaveBeenCalledTimes(1);
    expect(ampli.updatedPrimaryPlatformInProfile).toHaveBeenCalledWith({
      "Selected Platform(s)": ["Nintendo Switch"],
      Program: options.program
    });
  });

  it("logs 'Updated Secondary Platforms In Profile' event", () => {
    const ampli = { identify: jest.fn(), updatedSecondaryPlatformsInProfile: jest.fn() } as unknown as Ampli;
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        preferredPlatforms: [
          aPrimaryPlatform(),
          aSecondaryPlatform({ name: "Nintendo Switch" }),
          aSecondaryPlatform({ name: "PS4" })
        ]
      })
    );
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.updatedSecondaryPlatformsInProfile({
      locale,
      creator,
      selectedPlatforms: [{ label: "XBox" }, { label: "Nintendo Switch" }]
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      "Secondary Platform": ["XBox", "Nintendo Switch"]
    });
    expect(ampli.updatedSecondaryPlatformsInProfile).toHaveBeenCalledTimes(1);
    expect(ampli.updatedSecondaryPlatformsInProfile).toHaveBeenCalledWith({
      "Removed Platforms": ["PS4"],
      "Selected Platform(s)": ["XBox"],
      Program: options.program
    });
  });

  it("logs 'Started Join Opportunity Flow' event", () => {
    const ampli = { identify: jest.fn(), startedJoinOpportunityFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.startedJoinOpportunityFlow({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.startedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    expect(ampli.startedJoinOpportunityFlow).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Continued Join Opportunity Flow' event", () => {
    const ampli = { identify: jest.fn(), continuedJoinOpportunityFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.continuedJoinOpportunityFlow({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.continuedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    expect(ampli.continuedJoinOpportunityFlow).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Completed Join Opportunity Flow' event", () => {
    const ampli = { identify: jest.fn(), completedJoinOpportunityFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.completedJoinOpportunityFlow({ locale, opportunity });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    expect(ampli.completedJoinOpportunityFlow).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Cancelled Join Opportunity Flow' event", () => {
    const ampli = { identify: jest.fn(), cancelledJoinOpportunityFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.cancelledJoinOpportunityFlow({
      locale,
      opportunity,
      pageAbandoned: "/opportunities/bd1363f4-f372-49be-8853-8b5dbe95f521/?step=criteria"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.cancelledJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    expect(ampli.cancelledJoinOpportunityFlow).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      "Page Abandoned": "/opportunities/bd1363f4-f372-49be-8853-8b5dbe95f521/?step=criteria",
      Program: options.program
    });
  });

  it("logs 'Accepted Opportunity Invitation' event", () => {
    const ampli = { identify: jest.fn(), acceptedOpportunityInvitation: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.acceptedOpportunityInvitation({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.acceptedOpportunityInvitation).toHaveBeenCalledTimes(1);
    expect(ampli.acceptedOpportunityInvitation).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Declined Opportunity Invitation' event", () => {
    const ampli = { identify: jest.fn(), declinedOpportunityInvitation: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.declinedOpportunityInvitation({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.declinedOpportunityInvitation).toHaveBeenCalledTimes(1);
    expect(ampli.declinedOpportunityInvitation).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Clicked Email POC Link' event", () => {
    const ampli = { identify: jest.fn(), clickedEmailPocLink: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedEmailPocLink({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedEmailPocLink).toHaveBeenCalledTimes(1);
    expect(ampli.clickedEmailPocLink).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Email Sent To POC' event", () => {
    const ampli = { identify: jest.fn(), emailSentToPoc: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.emailSentToPoc({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.emailSentToPoc).toHaveBeenCalledTimes(1);
    expect(ampli.emailSentToPoc).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Checked Application Status' event", () => {
    const ampli = { identify: jest.fn(), checkedApplicationStatus: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.checkedApplicationStatus({ locale, status: "Accepted" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.checkedApplicationStatus).toHaveBeenCalledTimes(1);
    expect(ampli.checkedApplicationStatus).toHaveBeenCalledWith({
      "Application Status": "Accepted",
      Program: options.program
    });
  });

  it("logs 'Started Creator Application' event", () => {
    const ampli = { identify: jest.fn(), startedCreatorApplication: jest.fn() } as unknown as Ampli;

    const user = AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator);
    const interestedCreatorOptions = {
      user,
      program: "creator-network"
    };
    const analytics = new BrowserAnalytics(interestedCreatorOptions, ampli);

    analytics.startedCreatorApplication({ locale, page: "/interested-creators/start" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(interestedCreatorOptions.user.analyticsId, {
      Locale: locale,
      Type: "INTERESTED_CREATOR",
      Status: interestedCreatorOptions.user.status
    });
    expect(ampli.startedCreatorApplication).toHaveBeenCalledTimes(1);
    expect(ampli.startedCreatorApplication).toHaveBeenCalledWith({
      "Application Page": "/interested-creators/start",
      Program: options.program
    });
  });

  it("logs 'Started Creator Application' event with Identity type user", () => {
    const identity = Identity.fromStored(aStoredIdentity());
    const ampli = { identify: jest.fn(), startedCreatorApplication: jest.fn() } as unknown as Ampli;

    const user = AuthenticatedUserFactory.fromInterestedCreator(identity);
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.startedCreatorApplication({ locale, page: "/interested-creators/start" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, {
      Locale: locale,
      Type: options.user.type,
      Status: options.user.status
    });
    expect(ampli.startedCreatorApplication).toHaveBeenCalledTimes(1);
    expect(ampli.startedCreatorApplication).toHaveBeenCalledWith({
      "Application Page": "/interested-creators/start",
      Program: options.program
    });
  });

  it("logs 'Continued Creator Application' event", () => {
    const ampli = { identify: jest.fn(), continuedCreatorApplication: jest.fn() } as unknown as Ampli;
    const user = AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator);
    const interestedCreatorOptions = {
      user,
      program: "creator-network"
    };
    const analytics = new BrowserAnalytics(interestedCreatorOptions, ampli);
    const page = "/how-it-works";
    const finalStep = false;
    const creatorTypes = ["BLOGGER", "YOUTUBER"];

    analytics.continuedCreatorApplication({ locale, creatorTypes, page, finalStep });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(interestedCreatorOptions.user.analyticsId, {
      Locale: locale,
      "Creator Types": creatorTypes,
      Type: "INTERESTED_CREATOR",
      Status: interestedCreatorOptions.user.status
    });
    expect(ampli.continuedCreatorApplication).toHaveBeenCalledTimes(1);
    expect(ampli.continuedCreatorApplication).toHaveBeenCalledWith({
      "Application Page": page,
      "Final Step": finalStep,
      Program: options.program
    });
  });

  it("logs 'Cancelled Creator Application' event", () => {
    const ampli = { identify: jest.fn(), cancelledCreatorApplication: jest.fn() } as unknown as Ampli;
    const user = AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator);
    const interestedCreatorOptions = {
      user,
      program: "creator-network"
    };
    const analytics = new BrowserAnalytics(interestedCreatorOptions, ampli);

    analytics.cancelledCreatorApplication({ locale, page: "/interested-creators/creator-types" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(interestedCreatorOptions.user.analyticsId, {
      Locale: locale,
      Type: "INTERESTED_CREATOR",
      Status: interestedCreatorOptions.user.status
    });
    expect(ampli.cancelledCreatorApplication).toHaveBeenCalledTimes(1);
    expect(ampli.cancelledCreatorApplication).toHaveBeenCalledWith({
      "Application Page": "/interested-creators/creator-types",
      Program: options.program
    });
  });

  it("logs 'Submitted Website URL' event", () => {
    const ampli = { identify: jest.fn(), submittedWebsiteUrl: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.submittedWebsiteUrl({
      locale,
      opportunity,
      contentType: "text",
      websiteDomain: "reddit.com",
      deliverableTitle: "Test123",
      deliverableType: "SINGLE"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.submittedWebsiteUrl).toHaveBeenCalledTimes(1);
    expect(ampli.submittedWebsiteUrl).toHaveBeenCalledWith({
      "Content Type": "text",
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      "Website Domain Name": "reddit.com",
      "Deliverable Title": "Test123",
      "Deliverable Type": "SINGLE",
      Program: options.program
    });
  });

  it("logs 'Submitted File Upload' event", () => {
    const ampli = { identify: jest.fn(), submittedFileUpload: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.submittedFileUpload({
      locale,
      opportunity,
      contentType: "text",
      fileExtension: "txt",
      fileSize: 1280,
      deliverableTitle: "Test123",
      deliverableType: "SINGLE"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(ampli.submittedFileUpload).toHaveBeenCalledWith({
      "Content Type": "text",
      "File Extension": "txt",
      "File Size": "1.25 KB",
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      "Deliverable Title": "Test123",
      "Deliverable Type": "SINGLE",
      Program: options.program
    });
  });

  it("logs 'Submitted Social Content' event", () => {
    const ampli = { identify: jest.fn(), submittedSocialContent: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.submittedSocialContent({
      locale,
      opportunity,
      accountType: "YouTube",
      deliverableTitle: "Test123",
      deliverableType: "SINGLE"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.submittedSocialContent).toHaveBeenCalledTimes(1);
    expect(ampli.submittedSocialContent).toHaveBeenCalledWith({
      "Social Channel Type": "YouTube",
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      "Deliverable Title": "Test123",
      "Deliverable Type": "SINGLE",
      Program: options.program
    });
  });

  it("logs 'Connected New Social Account' event", () => {
    const ampli = { identify: jest.fn(), connectedNewSocialAccount: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.connectedNewSocialAccount({
      locale,
      accountType: "YouTube",
      deliverableTitle: "Test123",
      deliverableType: "SINGLE"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.connectedNewSocialAccount).toHaveBeenCalledTimes(1);
    expect(ampli.connectedNewSocialAccount).toHaveBeenCalledWith({
      "Social Channel Type": "YouTube",
      "Deliverable Title": "Test123",
      "Deliverable Type": "SINGLE",
      Program: options.program
    });
  });

  it("logs 'Started Content Submission Flow' event", () => {
    const ampli = { identify: jest.fn(), startedContentSubmissionFlow: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.startedContentSubmissionFlow({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.startedContentSubmissionFlow).toHaveBeenCalledTimes(1);
    expect(ampli.startedContentSubmissionFlow).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });

  it("logs 'Received Content Submission Error Message' event", () => {
    const ampli = { identify: jest.fn(), receivedContentSubmissionErrorMessage: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.receivedContentSubmissionErrorMessage({
      locale,
      opportunity,
      deliverableTitle: "Test1234",
      deliverableType: "SINGLE",
      errorCode: "409",
      errorMessage: "ERROR",
      submissionType: "WEBSITE"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.receivedContentSubmissionErrorMessage).toHaveBeenCalledTimes(1);
    expect(ampli.receivedContentSubmissionErrorMessage).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      "Error Code": "409",
      "Error Message": "ERROR",
      "Deliverable Title": "Test1234",
      "Type of Content Submission": "WEBSITE",
      "Deliverable Type": "SINGLE",
      Program: options.program
    });
  });

  it("logs 'Started Creator Application' event", () => {
    const ampli = { identify: jest.fn(), startedCreatorApplication: jest.fn() } as unknown as Ampli;
    const user = AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator);
    const interestedCreatorOptions = {
      user,
      program: "creator-network"
    };
    const analytics = new BrowserAnalytics(interestedCreatorOptions, ampli);

    analytics.startedCreatorApplication({ locale, page: "/interested-creators/start" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(interestedCreatorOptions.user.analyticsId, {
      Locale: locale,
      Type: "INTERESTED_CREATOR",
      Status: interestedCreatorOptions.user.status
    });
    expect(ampli.startedCreatorApplication).toHaveBeenCalledTimes(1);
    expect(ampli.startedCreatorApplication).toHaveBeenCalledWith({
      "Application Page": "/interested-creators/start",
      Program: options.program
    });
  });

  it("logs 'Clicked Payment Information In My Profile' event", () => {
    const ampli = { identify: jest.fn(), clickedPaymentDetailsIncompleteTooltip: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedPaymentDetailsIncompleteTooltip({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledTimes(1);
    expect(ampli.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Clicked Paid Opportunities When There Is No Transaction' event", () => {
    const ampli = {
      identify: jest.fn(),
      clickedPaidOpportunitiesWhenThereIsNoTransaction: jest.fn()
    } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedPaidOpportunitiesWhenThereIsNoTransaction({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedPaidOpportunitiesWhenThereIsNoTransaction).toHaveBeenCalledTimes(1);
    expect(ampli.clickedPaidOpportunitiesWhenThereIsNoTransaction).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Clicked Payment Details Incomplete Tooltip' event", () => {
    const ampli = { identify: jest.fn(), clickedPaymentDetailsIncompleteTooltip: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedPaymentDetailsIncompleteTooltip({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledTimes(1);
    expect(ampli.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledWith({ Program: options.program });
  });

  it("logs 'Clicked Payment Details Incomplete Helper Banner' event", () => {
    const ampli = { identify: jest.fn(), clickedPaymentDetailsIncompleteHelperBanner: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedPaymentDetailsIncompleteHelperBanner({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledTimes(1);
  });

  it("logs 'Clicked Payment Settings Tab' event", () => {
    const ampli = { identify: jest.fn(), clickedPaymentSettingsTab: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedPaymentSettingsTab({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedPaymentSettingsTab).toHaveBeenCalledTimes(1);
  });

  it("logs 'Clicked Opportunity Description' event", () => {
    const ampli = { identify: jest.fn(), clickedOpportunityDescription: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedOpportunityDescription({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedOpportunityDescription).toHaveBeenCalledTimes(1);
  });

  it("logs 'Downloaded Payment Contract' event", () => {
    const ampli = { identify: jest.fn(), downloadedPaymentContract: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.downloadedPaymentContract({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.downloadedPaymentContract).toHaveBeenCalledTimes(1);
  });

  it("logs 'Opened Payments Filters Form' event", () => {
    const ampli = { identify: jest.fn(), openedPaymentsFiltersForm: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.openedPaymentsFiltersForm({ locale });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
  });

  it("logs 'Applied Date Range Filter' event", () => {
    const ampli = { identify: jest.fn(), appliedDateRangeFilter: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.appliedDateRangeFilter({ locale, selectedDateRange: "allTime" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
    expect(ampli.appliedDateRangeFilter).toHaveBeenCalledWith({
      "Selected Date Range Option": "allTime",
      Program: options.program
    });
  });

  it("logs 'Applied Payment Status Filter' event", () => {
    const ampli = { identify: jest.fn(), appliedPaymentStatusFilter: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.appliedPaymentStatusFilter({ locale, selectedPaymentStatus: "PAID" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.appliedPaymentStatusFilter).toHaveBeenCalledTimes(1);
    expect(ampli.appliedPaymentStatusFilter).toHaveBeenCalledWith({
      "Payment Status Selected": "PAID",
      Program: options.program
    });
  });

  it("logs 'Applied Payment Type Filter' event", () => {
    const ampli = { identify: jest.fn(), appliedPaymentTypeFilter: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.appliedPaymentTypeFilter({ locale, selectedPaymentType: "marketing_opportunity" });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.appliedPaymentTypeFilter).toHaveBeenCalledTimes(1);
    expect(ampli.appliedPaymentTypeFilter).toHaveBeenCalledWith({
      "Payment Type Selected": "marketing_opportunity",
      Program: options.program
    });
  });

  it("logs 'Applied All Payment Filters' event", () => {
    const ampli = { identify: jest.fn(), appliedAllPaymentFilters: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.appliedAllPaymentFilters({
      locale,
      selectedPaymentStatus: "ALL",
      selectedPaymentType: "ALL",
      selectedDateRange: "thisMonth"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.appliedAllPaymentFilters).toHaveBeenCalledTimes(1);
    expect(ampli.appliedAllPaymentFilters).toHaveBeenCalledWith({
      "Payment Status Selected": "ALL",
      "Payment Type Selected": "ALL",
      "Selected Date Range Option": "thisMonth",
      Program: options.program
    });
  });

  it("logs 'Removed Payment Filter' event", () => {
    const ampli = { identify: jest.fn(), removedPaymentFilter: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.removedPaymentFilter({
      locale,
      removedFilterType: "range",
      removedFilteredValue: "allTime"
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.removedPaymentFilter).toHaveBeenCalledTimes(1);
    expect(ampli.removedPaymentFilter).toHaveBeenCalledWith({
      "Removed Filtered Value": "allTime",
      "Removed Filter Type": "Date Range",
      Program: options.program
    });
  });

  it("logs 'Clicked download attachment' event", () => {
    const ampli = { identify: jest.fn(), clickedDownloadAttachment: jest.fn() } as unknown as Ampli;
    const analytics = new BrowserAnalytics(options, ampli);

    analytics.clickedDownloadAttachment({
      locale,
      opportunity
    });

    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledWith(options.user.analyticsId, { Locale: locale });
    expect(ampli.clickedDownloadAttachment).toHaveBeenCalledTimes(1);
    expect(ampli.clickedDownloadAttachment).toHaveBeenCalledWith({
      "Opportunity Franchise": "FIFA 2021",
      "Opportunity ID": "c230bc75-faee-466e-a0e4-909be63427d7",
      "Opportunity Name": "EA Play",
      "Opportunity Perks": "TRAVEL",
      "Opportunity Platform": ["PS4"],
      "Opportunity Type": ["Game Codes"],
      "Opportunity Visibility": opportunity.visibility,
      Program: options.program
    });
  });
});
