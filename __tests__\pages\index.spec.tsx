import "reflect-metadata";
import { render, screen, waitFor, within } from "@testing-library/react";
import Home from "./../../pages/index";
import userEvent from "@testing-library/user-event";
import { aCreator } from "../factories/creators/Creator";
import { useDetectScreen } from "../../utils";
import config from "../../config";
import { mockMatchMedia } from "../helpers/window";
import { useRouter } from "next/router";
import "next/config";
import CreatorsService from "@src/api/services/CreatorsService";
import { useDependency } from "@src/context/DependencyContext";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../..//src/context/DependencyContext");
jest.mock("../../utils/hooks");
jest.mock("../../src/api/services/CreatorsService");

describe("Home", () => {
  mockMatchMedia();
  const homeProps = {
    interestedCreator: false,
    creatorTypesFallback: config.FALLBACK_CREATOR_TYPES,
    user: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      analytics: {},
      metadataClient: {},
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue([aCreatorType()])
    });
  });

  it("shows nav links on header web", async () => {
    (useDetectScreen as jest.Mock).mockImplementation((width) => width === 10000);

    render(<Home {...homeProps} />);

    const header = screen.getByTestId("header-web");
    const { getByText } = within(header); // nav links container
    await waitFor(() => {
      expect(header).toBeInTheDocument();
      expect(header).toContainElement(getByText(/home/<USER>
      expect(header).toContainElement(getByText(/work/i));
      expect(header).toContainElement(getByText(/about/i));
      expect(header).toContainElement(getByText(/signin/i));
      expect(header).toContainElement(getByText(/opportunities/i));
      expect(getByText(/home/<USER>
      expect(getByText(/home/<USER>"href", "/");
      expect(getByText(/works/i)).toBeInstanceOf(HTMLAnchorElement);
      expect(getByText(/works/i)).toHaveAttribute("href", "/how-it-works");
      expect(getByText(/opportunities/i)).toBeInstanceOf(HTMLAnchorElement);
      expect(getByText(/opportunities/i)).toHaveAttribute("href", "/opportunities-rewards");
      expect(getByText(/signin/i)).toBeInstanceOf(HTMLAnchorElement);
      expect(getByText(/signin/i)).toHaveAttribute("href", "/api/login");
    });
  });

  it("shows nav links on header mobile", async () => {
    (useDetectScreen as jest.Mock).mockImplementation((width) => width === 1279);
    render(<Home {...homeProps} />);
    const header = screen.getByTestId("header-mobile");

    await userEvent.click(screen.getByRole("button", { name: /expand/i }));

    await waitFor(() => {
      const homeLink = screen.getByRole("link", { name: /^home$/i });
      const howItWorksLink = screen.getByRole("link", { name: /works/i });
      const aboutSubMenu = screen.getByText(/about/i);
      const signInLink = screen.getByRole("link", { name: /signin/i });
      const opportunitiesLink = screen.getByRole("link", { name: /opportunities/i });

      // Verify link to home page
      expect(header).toContainElement(homeLink);
      expect(homeLink).toBeInstanceOf(HTMLAnchorElement);
      expect(homeLink).toHaveAttribute("href", "/");
      // Verify link to How it works page
      expect(header).toContainElement(howItWorksLink);
      expect(howItWorksLink).toBeInstanceOf(HTMLAnchorElement);
      expect(howItWorksLink).toHaveAttribute("href", "/how-it-works");
      // Verify link to sign in page
      expect(header).toContainElement(signInLink);
      expect(signInLink).toBeInstanceOf(HTMLAnchorElement);
      expect(signInLink).toHaveAttribute("href", "/api/login");
      // Verify link to opportunities page
      expect(header).toContainElement(opportunitiesLink);
      expect(opportunitiesLink).toBeInstanceOf(HTMLAnchorElement);
      expect(opportunitiesLink).toHaveAttribute("href", "/opportunities-rewards");
      // Verify about sub-menu
      expect(header).toContainElement(aboutSubMenu);
    });
  });

  it("shows dropdown option on hover of 'About'", async () => {
    (useDetectScreen as jest.Mock).mockImplementation((width) => width === 10000);
    render(<Home {...homeProps} />);
    const header = screen.getByTestId("header-container");
    const { getByText } = within(header); // nav links container
    const aboutButton = getByText(/about/i);

    await userEvent.hover(aboutButton);

    await waitFor(() => {
      expect(header).toBeInTheDocument();
      expect(header).toContainElement(getByText(/faq/i));
      expect(header).toContainElement(getByText(/policy/i));
      expect(header).toContainElement(getByText(/disclosure/i));
      expect(getByText(/faq/i)).toBeInstanceOf(HTMLSpanElement);
      expect(getByText(/policy/i)).toBeInstanceOf(HTMLSpanElement);
      expect(getByText(/disclosure/i)).toBeInstanceOf(HTMLSpanElement);
    });
  });

  it("shows request to join button in the header", async () => {
    const creator = aCreator();
    (CreatorsService.getCreatorWithPayableStatus as jest.Mock).mockResolvedValue({
      data: creator
    });

    render(<Home {...homeProps} interestedCreator={true} />);

    await waitFor(() => {
      expect(screen.getByText("requestToJoin")).toBeInTheDocument();
    });
  });
});
