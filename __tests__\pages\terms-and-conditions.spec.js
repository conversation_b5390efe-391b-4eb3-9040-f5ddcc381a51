import "reflect-metadata";
import { act, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import TermsAndConditions from "../../pages/terms-and-conditions";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import { aLegalEntity } from "../factories/creators/LegalEntity";
import { aMailingAddress } from "../factories/creators/MailingAddress";
import { anAccountInformationWithPayableStatus } from "../factories/creators/AccountInformationWithPayableStatus";
import { mockMatchMedia, mockWindowReload, restoreWindowReload } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import CreatorsService, { CreatorWithCreatorCodeProfile } from "../../src/api/services/CreatorsService";
import TermsAndConditionsService from "../../src/api/services/TermsAndConditionsService";
import Random from "../factories/Random";
import config from "../../config";
import { useAppContext } from "../../src/context";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import { onToastClose } from "../../utils";
import { aLocalizedDate } from "../factories/LocalizedDateBuilder";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";
import { aCountry } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../src/api/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../src/api/services/CreatorsService"),
    update: jest.fn(),
    getCreatorWithTier: jest.fn()
  };
});
jest.mock("../../src/api/services/TermsAndConditionsService");
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  onToastClose: jest.fn()
}));

describe("TermsAndConditions", () => {
  mockMatchMedia();
  const SUPPORTED_LOCALES = config.SUPPORTED_LOCALES;
  const mockUseDetectScreen = jest.fn();
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY");
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/onboarding/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  const router = {
    locale: "ja-jp",
    push: jest.fn(),
    pathname: "/terms-and-conditions"
  };
  const countries = [aCountry()];
  const metadataService = {
    getCountries: jest.fn().mockResolvedValue(countries)
  };
  const analytics = {
    signedTermsAndConditions: jest.fn(),
    completedOnboardingFlow: jest.fn(),
    canceledOnboardingFlow: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useDependency.mockReturnValue({
      analytics,
      metadataClient: {},
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
    useRouter.mockImplementation(() => router);
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps,
        userNavigated: false
      }
    });
    MetadataService.mockReturnValue(metadataService);
  });

  it("disables Next button if no legal address is provided", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: { status: "INACTIVE" },
        legalInformation: { entityType: "INDIVIDUAL", businessName: null },
        communicationPreferences: { email: "<EMAIL>" }
      })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    await waitFor(() => {
      expect(screen.getByLabelText(/sameaddress/i)).not.toBeChecked();
      expect(screen.getByLabelText(/^terms-and-conditions:street/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:city/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:state/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:zip/i)).not.toBeDisabled();
      expect(screen.getByRole("button", { name: /Next/i })).toBeDisabled();
      // Country is not an actual select, skipping for now...
    });
  });

  it("enables Next button if legal address is provided", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "INACTIVE" })
      })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    await waitFor(() => {
      expect(screen.getByLabelText(/sameaddress/i)).not.toBeChecked(); // since addresses are different
      expect(screen.getByLabelText(/^terms-and-conditions:street/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:city/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:state/i)).not.toBeDisabled();
      expect(screen.getByLabelText(/^terms-and-conditions:zip/i)).not.toBeDisabled();
      expect(screen.getByRole("button", { name: /Next/i })).not.toBeDisabled();
      // Country is not an actual select, skipping for now...
    });
  });

  it("disables address fields if same address checkbox is checked", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "INACTIVE" }, SUPPORTED_LOCALES)
      })
    });
    renderPage(<TermsAndConditions locale="en-us" />);
    const checkBox = await screen.findByLabelText(/sameaddress/i);
    expect(checkBox).not.toBeChecked();

    await userEvent.click(checkBox);

    expect(checkBox).toBeChecked(); // since addresses are different
    expect(await screen.findByLabelText(/^terms-and-conditions:street/i)).toBeDisabled();
    expect(await screen.findByLabelText(/^terms-and-conditions:city/i)).toBeDisabled();
    expect(await screen.findByLabelText(/^terms-and-conditions:state/i)).toBeDisabled();
    expect(await screen.findByLabelText(/^terms-and-conditions:zip/i)).toBeDisabled();
    expect(screen.getByRole("button", { name: /Next/i })).not.toBeDisabled();
    // Country is not an actual select, skipping for now...
  });

  it("shows business name if entity type is business", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({ legalInformation: aLegalEntity({ entityType: "BUSINESS" }) })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    await waitFor(async () => {
      expect(await screen.findByLabelText(/^terms-and-conditions:businessName/i)).toBeInTheDocument();
    });
  });

  it("pre-populates country from legal address", async () => {
    const countries = [
      { value: "US", label: "United States", name: "United States" },
      { value: "MX", label: "Mexico", name: "Mexico" }
    ];
    const creator = aCreatorWithPayableStatus({
      accountInformation: anAccountInformationWithPayableStatus({ status: "INACTIVE" }),
      legalInformation: aLegalEntity({ entityType: "BUSINESS", country: { code: "MX" } })
    });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue(countries);

    renderPage(<TermsAndConditions locale="en-us" />);

    await screen.findByText("Mexico");
  });

  it("pre-populates country from mailing address", async () => {
    const countries = [
      { value: "US", label: "United States", name: "United States" },
      { value: "MX", label: "Mexico", name: "Mexico" },
      { value: "CA", label: "Canada", name: "Canada" }
    ];
    const creator = aCreatorWithPayableStatus({
      mailingAddress: aMailingAddress({
        country: { code: "CA", name: "Canada" },
        state: "Nova Scotia",
        city: "Halifax",
        zipCode: "B4C2J5",
        street: "Old Sackville rd"
      }),
      legalInformation: aLegalEntity({
        entityType: "BUSINESS",
        country: { code: "CA", name: "Canada" },
        state: "Nova Scotia",
        city: "Halifax",
        zipCode: "B4C2J5",
        street: "Old Sackville rd"
      })
    });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue(countries);

    renderPage(<TermsAndConditions locale="en-us" />);

    await waitFor(async () => {
      expect(await screen.findByLabelText(/sameaddress/i)).toBeChecked(); // since addresses are same
      expect(screen.getByDisplayValue("Canada")).toBeInTheDocument();
    });
  });

  it("shows welcome message for new users", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "UNREGISTERED" })
      })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    expect(await screen.findByRole("heading", { name: /terms-and-conditions:title/i })).toBeInTheDocument();
    expect(screen.getByText(/terms-and-conditions:subTitleNewUser/i)).toBeInTheDocument();
  });

  it("shows welcome back message for inactive users", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "INACTIVE" })
      })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    expect(await screen.findByRole("heading", { name: /terms-and-conditions:title/i })).toBeInTheDocument();
    expect(screen.getByText(/terms-and-conditions:subTitleExistingUser/i)).toBeInTheDocument();
  });

  it("shows invitation to sign new terms and conditions", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "ACTIVE" })
      })
    });

    renderPage(<TermsAndConditions locale="en-us" />);

    await waitFor(async () => {
      expect(screen.getByText(/terms-and-conditions:titleUpdatedTermsAndConditions/i)).toBeInTheDocument();
      expect(screen.getByText(/terms-and-conditions:subTitleUpdatedTermsAndConditions/i)).toBeInTheDocument();
    });
  });

  it("updates creator with status active and makes clear cache call", async () => {
    const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
    const locale = "en-us";
    useRouter.mockImplementation(() => ({ ...router, locale: locale }));
    const accountInformation = anAccountInformationWithPayableStatus({
      status: "ACTIVE",
      dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years),
      dateOfBirthAsText: LocalizedDate.epochMinusMonths(240),
      registrationDate: LocalizedDate.epochMinusDays(30)
    });
    const creator = aCreatorWithPayableStatus({ accountInformation: accountInformation });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: Random.url() }
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    const response = new CreatorWithCreatorCodeProfile(creator); // TODO: this will make the test pass, but is it expected?
    expect(await screen.findByRole("button", { name: /Next/i })).toBeInTheDocument();

    await waitFor(() => {
      const event = new Event("message");
      event.data = { event: "request_signed" };
      event.origin = "https://pactsafe.com/sign";
      window.dispatchEvent(event);
    });

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1); // why is it called twice?
      expect(CreatorsService.update).toHaveBeenCalledWith({
        accountInformation: {
          ...response.accountInformation,
          dateOfBirth: LocalizedDate.format(response.accountInformation.dateOfBirth.toDate(), "YYYY-MM-DD"),
          registrationDate: { ...response.accountInformation.registrationDate }
        }
      });
      expect(TermsAndConditionsService.clearSignedStatusForTier).toHaveBeenCalledTimes(1);
    });
  });

  it("shows modal with link to log out", async () => {
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "UNREGISTERED" })
      })
    });
    renderPage(<TermsAndConditions locale="en-us" />);

    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    const locale = "ja-jp";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "UNREGISTERED" })
      })
    });
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    await userEvent.click(await screen.findByText("yes"));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({ locale: locale, page: "/terms-and-conditions" });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Signed Terms And Conditions' event when user signed/cancel the terms and conditions", async () => {
    const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
    mockWindowReload();
    const locale = "ja-jp";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      isPayable: true,
      status: "UNREGISTERED",
      dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years),
      dateOfBirthAsText: LocalizedDate.epochMinusMonths(240),
      registrationDate: LocalizedDate.epochMinusDays(30),
      creatorCode: null
    };
    const creator = aCreatorWithPayableStatus({
      accountInformation: anAccountInformationWithPayableStatus(accountInformation)
    });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: "https://pactsafe.com" }
    });
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));
    const requestSignedEvent = new Event("message");
    requestSignedEvent.data = { event: "request_signed" };
    requestSignedEvent.origin = "https://pactsafe.com/sign";
    act(() => {
      window.dispatchEvent(requestSignedEvent);
    });

    await waitFor(() => {
      expect(analytics.signedTermsAndConditions).toHaveBeenCalledTimes(1);
      expect(analytics.signedTermsAndConditions).toHaveBeenCalledWith({ accepted: true, locale: locale });
      expect(analytics.completedOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedOnboardingFlow).toHaveBeenCalledWith({ locale: locale });
    });

    // TODO: this should be a separate test case
    const cancelEvent = new Event("message");
    cancelEvent.data = { event: "canceled" };
    cancelEvent.origin = "https://pactsafe.com/sign";
    act(() => {
      window.dispatchEvent(cancelEvent);
    });

    await waitFor(() => {
      expect(analytics.signedTermsAndConditions).toHaveBeenCalledTimes(2);
      expect(analytics.signedTermsAndConditions).toHaveBeenCalledWith({ accepted: false, locale: locale });
    });
    restoreWindowReload();
  });

  it("logs 'Completed Onboarding Flow' event when unregistered user signed the terms and conditions", async () => {
    const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
    const locale = "ja-jp";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      isPayable: true,
      status: "UNREGISTERED",
      dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years),
      dateOfBirthAsText: LocalizedDate.epochMinusMonths(240),
      registrationDate: LocalizedDate.epochMinusDays(30),
      creatorCode: null
    };
    const creator = aCreatorWithPayableStatus({
      accountInformation: anAccountInformationWithPayableStatus(accountInformation)
    });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: "https://pactsafe.com" }
    });
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    const event = new Event("message");
    event.data = { event: "request_signed" };
    event.origin = "https://pactsafe.com/sign";
    act(() => {
      window.dispatchEvent(event);
    });

    await waitFor(() => {
      expect(analytics.completedOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedOnboardingFlow).toHaveBeenCalledWith({ locale: locale });
    });
  });

  it("navigates to Communication Preferences page when user click on 'Back' button", async () => {
    const locale = "en-us";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      isPayable: false,
      status: "UNREGISTERED",
      dateOfBirth: dateOfBirthAfter18years,
      dateOfBirthAsText: "12月 31, 1969",
      registrationDate: "2020-12-31"
    };
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(
        aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
      )
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: "https://pactsafe.com" }
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<TermsAndConditions locale={locale} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/communication-preferences"));
  });

  it("shows a decline modal when clicking on 'Decline' button", async () => {
    const locale = "ja-jp";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      status: "UNREGISTERED",
      dateOfBirth: dateOfBirthAfter18years,
      dateOfBirthAsText: "12月 31, 1969",
      registrationDate: "Invalid date"
    };
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(
        aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
      )
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
    });
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await userEvent.click(await screen.findByRole("button", { name: /Decline/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /Cancel/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /declineTermsAndCondition/i })).toBeInTheDocument();
    expect(await screen.findByText(/terms-and-conditions:declineModalHeader/i)).toBeInTheDocument();
    expect(await screen.findByText(/terms-and-conditions:declineModalDescription/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Decline' button in the decline modal", async () => {
    const locale = "ja-jp";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      status: "UNREGISTERED",
      dateOfBirth: dateOfBirthAfter18years,
      dateOfBirthAsText: "12月 31, 1969",
      registrationDate: "Invalid date"
    };
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(
        aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
      )
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
    });
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));
    await userEvent.click(await screen.findByRole("button", { name: /Decline/i }));

    await userEvent.click(await screen.findByRole("button", { name: /declineTermsAndCondition/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        locale: locale,
        page: "/terms-and-conditions"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("updates legal address if mailing and legal address are not same", async () => {
    const locale = "en-us";
    const creatorCountryName = "Canada";
    const creatorCountry = { name: creatorCountryName, code: "CA" };
    const countries = [creatorCountry];
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      // TODO: fix test factory which is wrongly passing a string to country `aCountry` (type is wrong)
      legalInformation: aLegalEntity({ country: creatorCountry })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    metadataService.getCountries = jest.fn().mockResolvedValue(countries);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: {}
    });
    renderPage(<TermsAndConditions locale={locale} />);
    let sameAddressCheckBox;
    await waitFor(() => {
      sameAddressCheckBox = screen.queryByLabelText(/sameaddress/i);
      expect(sameAddressCheckBox).toBeInTheDocument();
    });
    await userEvent.click(sameAddressCheckBox);
    expect(sameAddressCheckBox).not.toBeChecked();
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
  });

  it("detects viewport size and shows page back button for mobile", async () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    renderPage(<TermsAndConditions locale="en-US" />);

    expect(await screen.findByRole("button", { name: /Back/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /Back/i })).toHaveClass("display-back-bt");
  });

  it("show confirmation modal when user click on 'Back' button with form has unsaved changes", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    // There are two cancel buttons in this page used within to find the one in the modal
    const { findByRole, findByText } = within(await screen.findByRole("dialog"));
    expect(await findByRole("heading", { name: /breadcrumb:modalTitle/i })).toBeInTheDocument();
    expect(await findByText(/breadcrumb:modalMessage/i)).toBeInTheDocument();
    expect(await findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /discard/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /save/i })).toBeInTheDocument();
    expect(router.push).not.toBeCalled();
  });

  it("closes the confirmation modal on discard", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /discard/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
    expect(router.push).toHaveBeenCalledWith("/communication-preferences");
  });

  it("saves the form from confirmation modal and navigates back", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/communication-preferences");
  });

  it("closes the confirmation modal", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /Close$/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
  });

  it("saves the form from confirmation modal and navigates with stepper links", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);
    await userEvent.click(await screen.findByRole("button", { name: /Information/i }));
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/onboarding/information");
  });

  it("doesn't navigate back while editing the address checkbox after cancelling the confirmation modal", async () => {
    const locale = "en-us";
    const creator = aCreatorWithPayableStatus({
      accountInformation: { status: "UNREGISTERED" },
      legalInformation: aLegalEntity({ country: aCountry({ name: "Canada", code: "CA" }) })
    });
    const newStreetAddress = "75575 Lisandro Ridge";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: creator
    });
    metadataService.getCountries = jest.fn().mockResolvedValue([aCountry({ name: "Canada", code: "CA" })]);
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    await userEvent.click(await screen.findByLabelText(/sameaddress/i));
    const streetInput = screen.getByLabelText(/^terms-and-conditions:street/i);
    // Change street in legal address
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, newStreetAddress);
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));
    await userEvent.click(await findByRole("button", { name: /Cancel/i }));
    const checkBox = await screen.findByLabelText(/sameaddress/i);
    expect(checkBox).not.toBeChecked();

    await userEvent.click(checkBox);

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
    expect(checkBox).toBeChecked(); // since addresses are different
    expect(router.push).not.toHaveBeenCalledWith("/communication-preferences");
  });

  it("navigates to 'Connect Accounts' page when user click on stepper links without updating any form fields", async () => {
    const locale = "en-us";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      isPayable: false,
      status: "UNREGISTERED",
      dateOfBirth: dateOfBirthAfter18years,
      dateOfBirthAsText: "12月 31, 1969",
      registrationDate: "2020-12-31"
    };
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(
        aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
      )
    });
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: "https://pactsafe.com" }
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<TermsAndConditions locale={locale} />);

    await userEvent.click(await screen.findByRole("button", { name: /Connect Accounts/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/connect-accounts"));
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage, userNavigated: true, onboardingSteps: steps }
    });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({ status: "UNREGISTERED" })
      })
    });
    const { unmount } = renderWithToast(<TermsAndConditions locale="en-us" />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: "close" }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("clears cache after signing new T&Cs", async () => {
    const locale = "en-us";
    useRouter.mockImplementation(() => ({ locale: locale, push: jest.fn() }));
    const accountInformation = anAccountInformationWithPayableStatus({
      status: "ACTIVE",
      dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years),
      dateOfBirthAsText: LocalizedDate.epochFromFormattedDate("1969-12-31"),
      registrationDate: LocalizedDate.epochFromFormattedDate("2020-12-31")
    });
    const creator = aCreatorWithPayableStatus({ accountInformation: accountInformation });
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(creator)
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    renderPage(<TermsAndConditions locale={locale} />);
    expect(await screen.findByRole("button", { name: /Next/i })).toBeInTheDocument();

    await waitFor(() => {
      const event = new Event("message");
      event.data = { event: "request_signed" };
      event.origin = "https://pactsafe.com/sign";
      window.dispatchEvent(event);
    });

    await waitFor(() => {
      expect(TermsAndConditionsService.clearSignedStatusForTier).toHaveBeenCalledTimes(1);
    });
  });

  it("navigates to user signed/cancel the terms and conditions page", async () => {
    const locale = "ja-jp";
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(aCreatorWithPayableStatus())
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: aCreatorWithPayableStatus()
    });
    renderPage(<TermsAndConditions locale={locale} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    expect(await screen.findByRole("button", { name: /DECLINE/i })).toBeInTheDocument();
  });

  it("navigates to communication Preferences page when user click on 'Back' button from signed/cancel T&C page", async () => {
    const locale = "en-us";
    const accountInformation = {
      defaultGamerTag: "nostrum",
      nucleusId: 68955,
      firstName: "Elian",
      lastName: "Koch",
      originEmail: "<EMAIL>",
      needsMigration: true,
      isPayable: false,
      status: "UNREGISTERED",
      dateOfBirth: dateOfBirthAfter18years,
      dateOfBirthAsText: "12月 31, 1969",
      registrationDate: "2020-12-31"
    };
    CreatorsService.getCreatorWithTier.mockResolvedValue({
      data: new CreatorWithCreatorCodeProfile(
        aCreatorWithPayableStatus({ accountInformation: anAccountInformationWithPayableStatus(accountInformation) })
      )
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.clearSignedStatusForTier.mockImplementation(() => Promise.resolve());
    TermsAndConditionsService.getSigningUrl.mockResolvedValue({
      data: { contractUrl: "https://pactsafe.com" }
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<TermsAndConditions locale={locale} />);
    expect(await screen.findByRole("button", { name: /Next/i })).toBeInTheDocument();
    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/communication-preferences"));
  });

  xdescribe("with 'FLAG_COUNTRIES_BY_TYPE' flag enabled", () => {
    it("pre-populates country from mailing address", async () => {
      const countries = [
        { value: "US", label: "United States", name: "United States" },
        { value: "MX", label: "Mexico", name: "Mexico" },
        { value: "CA", label: "Canada", name: "Canada" }
      ];
      const creator = aCreatorWithPayableStatus({
        mailingAddress: aMailingAddress({
          country: { code: "CA", name: "Canada" },
          state: "Nova Scotia",
          city: "Halifax",
          zipCode: "B4C2J5",
          street: "Old Sackville rd"
        }),
        legalInformation: aLegalEntity({
          entityType: "BUSINESS",
          country: { code: "CA", name: "Canada" },
          state: "Nova Scotia",
          city: "Halifax",
          zipCode: "B4C2J5",
          street: "Old Sackville rd"
        })
      });
      CreatorsService.getCreatorWithTier.mockResolvedValue({
        data: creator
      });
      metadataService.getCountriesMatching = jest.fn().mockResolvedValue(countries);

      renderPage(<TermsAndConditions locale="en-us" FLAG_COUNTRIES_BY_TYPE />);

      await waitFor(async () => {
        expect(screen.getByDisplayValue("Canada")).toBeInTheDocument();
        expect(metadataService.getCountriesMatching).toHaveBeenCalledTimes(1);
        expect(metadataService.getCountriesMatching).toHaveBeenCalledWith();
      });
    });
  });
});
