import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Registrations from "../../../../pages/opportunities/[id]/registrations";
import OpportunityService from "../../../../src/api/services/OpportunityService";
import OperationsService from "../../../../src/api/services/OperationsService";
import { mockMatchMedia } from "../../../helpers/window";
import { useRouter } from "next/router";
import { renderPage } from "../../../helpers/page";
import { aCreator } from "../../../factories/creators/Creator";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import OpportunityWithDeliverablesResponse from "../../../../src/opportunities/OpportunityWithDeliverables";
import { useDependency } from "../../../../src/context/DependencyContext";

jest.mock("../../../../utils", () => ({
  ...jest.requireActual("../../../../utils"),
  useDetectScreen: jest.fn().mockImplementation((width) => width === 1279)
}));
jest.mock("../../../../src/api/services/OperationsService");
jest.mock("../../../../src/api/services/OpportunityService");
jest.mock("../../../../src/context/DependencyContext");

describe("Registrations", () => {
  mockMatchMedia();
  const opportunity = OpportunityWithDeliverablesResponse.fromApi(
    aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      hasGameCodes: true,
      id: "OPPO124"
    })
  );
  const router = { query: { id: "OPPO124" }, locale: "en-us" };
  const analytics = {
    startedJoinOpportunityFlow: jest.fn(),
    completedJoinOpportunityFlow: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    useDependency.mockReturnValue({ analytics, errorHandler: jest.fn() });
  });

  it("displays title in Join Opportunity Criteria page", async () => {
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
  });

  it("logs 'Started Join Opportunity Flow' event after accepting the opportunity criteria", async () => {
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OperationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );
    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
    expect(await screen.findByRole("button", { name: "next" }, { timeout: 1_100 })).toBeDisabled();
    const checkboxInput = screen.getByRole("checkbox");
    // Accept opportunity criteria
    await userEvent.click(checkboxInput);
    await waitFor(async () => {
      expect(checkboxInput).toBeChecked();
      expect(await screen.findByRole("button", { name: "next" })).toBeEnabled();
    });

    await userEvent.click(await screen.findByRole("button", { name: "next" }));

    await waitFor(() => {
      expect(analytics.startedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.startedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  it("logs 'Completed Join Opportunity Flow' event after accepting the opportunity criteria", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      hasGameCodes: false,
      hasDeliverables: false
    });
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OperationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );
    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
    const joinButton = await screen.findByRole("button", { name: "join" });
    expect(joinButton).toBeDisabled();
    const checkboxInput = screen.getByRole("checkbox");
    // Accept opportunity criteria
    await userEvent.click(checkboxInput);
    await waitFor(() => {
      expect(checkboxInput).toBeChecked();
      expect(joinButton).toBeEnabled();
    });

    await userEvent.click(joinButton);

    await waitFor(() => {
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
      expect(OperationsService.saveParticipation).toHaveBeenCalledTimes(1);
    });
  });
});
