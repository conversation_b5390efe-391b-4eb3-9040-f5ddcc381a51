import { Container } from "typedi";
import {
  AuthorizationE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TermsAndConditionsStatusesHttpClient,
  VerifyAccessToProgramServerPropsMiddleware
} from "@eait-playerexp-cn/identity";
import EAAuthenticationProvider from "./identity/AuthenticationProvider";
import LegacyCreatorsHttpClient from "./creators/CreatorsHttpClient";
import OpportunitiesHttpClient from "./opportunities/OpportunitiesHttpClient";
import ValidRegistrationCodesHttpClient from "./validRegistrationCodes/ValidRegistrationCodesHttpClient";
import config from "../config";
import TermsAndConditionsHttpClient from "./pactSafe/TermsAndConditionsHttpClient";
import PaymentInformationHttpClient from "./paymentInformation/PaymentInformationHttpClient";
import OpportunityRegistrationsHttpClient from "./opportunities/OpportunityRegistrationsHttpClient";
import ParticipationsHttpClient from "./opportunities/ParticipationsHttpClient";
import DiscordAccountHttpClient from "./channels/DiscordAccountHttpClient";
import NotificationsHttpClient from "./notifications/NotificationsHttpClient";
import MicroCopiesHttpClient from "./contentManagement/MicroCopiesHttpClient";
import CachedAccessTokenProvider from "./tokens/CachedAccessTokenProvider";
import CachedTermsAndConditions from "./pactSafe/CachedTermsAndConditions";
import SearchCreatorsWithFlaggedStatusHttpClient from "./creators/SearchCreatorsWithFlaggedStatusHttpClient";
import { AttributesConverter, AttributesSerializer, CompositeCollector } from "@eait-playerexp-cn/telemetry";
import { CompositeSerializer, ExceptionSerializer, ObjectMapper } from "@eait-playerexp-cn/object-mapper";
import { NotificationsBellMapper } from "./contentManagement/NotificationsBellMapper";
import { NotificationsPageMapper } from "./contentManagement/NotificationsPageMapper";
import { NoAccountPageMapper } from "./contentManagement/NoAccountPageMapper";
import ApplicationHeadersProvider from "./api/ApplicationHeadersProvider";
import {
  anAuthorizationHeaderProvider,
  anHttpClient,
  anOAuthTokenProvider,
  aTracer,
  CompositeHeadersProvider,
  HttpClientTracer,
  LoggingCollector,
  OAuthBearerHeaderProvider,
  OAuthTokenProvider
} from "@eait-playerexp-cn/http-client";
import { ActivityFeed } from "@eait-playerexp-cn/activity-feed";
import { ActivityLogger, ContextSerializer, Logger } from "@eait-playerexp-cn/activity-logger";
import SentryRecorder from "@src/logging/SentryRecorder";
import TelemetryRecorder from "@src/logging/TelemetryRecorder";
import {
  ApiRouteErrorHandler,
  ErrorLoggerServerPropsMiddleware,
  JsonErrorHandler,
  AuthorizationErrorHandler as LegacyAuthorizationErrorHandler,
  MicroServiceErrorHandler,
  RedisCache,
  RedisClientFactory,
  RequestFactory,
  SessionOptionsFactory
} from "@eait-playerexp-cn/server-kernel";
import LegacyOpportunitiesHttpClient from "./opportunities/LegacyOpportunitiesHttpClient";
import AuthenticateCreatorAction from "@src/actions/AuthenticateCreatorAction";
import AuthenticateCreatorWithFlaggedStatusAction from "./actions/AuthenticateCreatorWithFlaggedStatusAction";
import EmailsHttpClient from "./opportunities/EmailsHttpClient";
import ContentManagementService from "./api/services/ContentManagementService";
import CreatorsWithProgramHttpClient from "./creators/CreatorsWithProgramsHttpClient";
import {
  ContinueOnboardingPlugin,
  RegistrationCodesHttpClient,
  SaveRegistrationCodeToSessionController,
  StartOnboardingPlugin,
  VerifyIncompleteRegistrationPropsMiddleware
} from "@eait-playerexp-cn/onboarding-authentication-plugins";
import {
  ContinueRequestToJoinPlugin,
  NoAccountPlugin,
  RequestsToJoinHttpClient,
  StartApplicationController,
  StartRequestToJoinPlugin,
  UnderageRequestToJoinPlugin,
  VerifyRequestToJoinServerPropsMiddleware
} from "@eait-playerexp-cn/interested-creators-authentication-plugins";
import {
  AuthenticateController,
  AuthenticationPlugin,
  Authenticator,
  AuthenticatorPlugin,
  CloseSessionController,
  CompositeAuthenticator,
  CreatorsHttpClient,
  DisabledAccountPlugin,
  InactiveAccountPlugin,
  PlayerProfilesHttpClient,
  RedirectToLoginController,
  RedirectToLogoutController,
  ShowInitialMessagePlugin,
  SuccessfulAuthenticationPlugin,
  UpToDateTermsAndConditionsPlugin
} from "@eait-playerexp-cn/authentication";
import ConnectedAccountsHttpClient from "./accounts/ConnectedAccountsHttpClient";
import { CommonPageMapper } from "./contentManagement/CommonPageMapper";
import { AgeRestrictionPageMapper } from "./contentManagement/AgeRestrictionPageMapper";
import { ApplicationAcceptedPageMapper } from "./contentManagement/ApplicationAcceptedPageMapper";
import { ApplicationRejectedPageMapper } from "./contentManagement/ApplicationRejectedPageMapper";
import { ApplicationCompletePageMapper } from "./contentManagement/ApplicationCompletePageMapper";
import { ApplicationStartPageMapper } from "./contentManagement/ApplicationStartPageMapper";
import { InformationPageMapper } from "./contentManagement/InformationPageMapper";
import { CommunicationPreferencesPageMapper } from "./contentManagement/CommunicationPreferencesPageMapper";
import { BreadcrumbPageMapper } from "./contentManagement/BreadcrumbPageMapper";
import { AddContentPageMapper } from "./contentManagement/AddContentPageMapper";
import { ConnectAccountsPageMapper } from "./contentManagement/ConnectAccountsPageMapper";
import { CreatorTypePageMapper } from "./contentManagement/CreatorTypePageMapper";
import { FranchisesYouPlayPageMapper } from "./contentManagement/FranchisesYouPlayPageMapper";
import { ApplicationPendingPageMapper } from "./contentManagement/ApplicationPendingPageMapper";
// import ConnectYouTubeAccountController from "./controllers/ConnectYouTubeAccountController";
// import ConnectTikTokAccountController from "./controllers/ConnectTikTokAccountController";
// import ConnectTwitchAccountController from "./controllers/ConnectTwitchAccountController";
// import ConnectInstagramAccountController from "./controllers/ConnectInstagramAccountController";
// import SaveFacebookPagesController from "./controllers/SaveFacebookPagesController";
// import ConnectDiscordAccountController from "./controllers/ConnectDiscordAccountController";

const ApiContainer = Container.of("api");

ApiContainer.set(ExceptionSerializer, new ExceptionSerializer());
ApiContainer.set(CompositeSerializer, new CompositeSerializer([ApiContainer.get(ExceptionSerializer)]));
ApiContainer.set(ObjectMapper, new ObjectMapper(ApiContainer.get(CompositeSerializer)));
ApiContainer.set(ContextSerializer, new ContextSerializer(ApiContainer.get(ObjectMapper)));
ApiContainer.set(AttributesSerializer, new AttributesSerializer(ApiContainer.get(ObjectMapper)));
ApiContainer.set(AttributesConverter, new AttributesConverter(ApiContainer.get(AttributesSerializer)));
ApiContainer.set(
  ActivityFeed,
  new ActivityFeed([
    new TelemetryRecorder(),
    new ActivityLogger(new Logger(config.LOG_LEVEL), ApiContainer.get(ContextSerializer)),
    new SentryRecorder()
  ])
);
ApiContainer.set(CompositeCollector, new CompositeCollector([new LoggingCollector(ApiContainer.get(ActivityFeed))]));
ApiContainer.set(
  HttpClientTracer,
  aTracer()
    .withName(config.SERVICE_NAME)
    .withSerializer(ApiContainer.get(CompositeSerializer))
    .withLoggingCollector(ApiContainer.get(ActivityFeed))
    .build()
);
ApiContainer.set("supportedLocales", config.SUPPORTED_LOCALES);
ApiContainer.set("options", {
  supportedLocales: config.SUPPORTED_LOCALES,
  program: config.PROGRAM_CODE,
  feed: ApiContainer.get(ActivityFeed)
});
ApiContainer.set(
  "sessionOptions",
  new SessionOptionsFactory(config.sessionOptions).create(RedisClientFactory.create(config.redisClient))
);
ApiContainer.set(RedisCache, new RedisCache(config.cacheOptions, ApiContainer.get(ActivityFeed)));
ApiContainer.set(
  OAuthTokenProvider,
  anOAuthTokenProvider()
    .withBaseUrl(config.ACCESS_TOKEN_BASE_URL)
    .withRequestTimeout(+config.HTTP_REQUEST_TIMEOUT)
    .withClientCredentials(config.API_CLIENT_ID, config.API_CLIENT_SECRET)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .build()
);
ApiContainer.set(
  CachedAccessTokenProvider,
  new CachedAccessTokenProvider(ApiContainer.get(OAuthTokenProvider), ApiContainer.get(RedisCache))
);
ApiContainer.set(
  OAuthBearerHeaderProvider,
  anAuthorizationHeaderProvider().withOAuthTokenProvider(ApiContainer.get(CachedAccessTokenProvider)).build()
);
ApiContainer.set(ApplicationHeadersProvider, new ApplicationHeadersProvider());
ApiContainer.set(RequestFactory, new RequestFactory());
ApiContainer.set(
  JsonErrorHandler,
  config.FLAG_PER_PROGRAM_PROFILE
    ? new JsonErrorHandler([
        new MicroServiceErrorHandler(),
        new AuthorizationErrorHandler(ApiContainer.get("options"), ApiContainer.get(RequestFactory)),
        new ApiRouteErrorHandler(
          ApiContainer.get("options"),
          config.APP_DEBUG,
          ApiContainer.get(ExceptionSerializer),
          ApiContainer.get(RequestFactory)
        )
      ])
    : new JsonErrorHandler([
        new MicroServiceErrorHandler(),
        new LegacyAuthorizationErrorHandler(ApiContainer.get("options"), ApiContainer.get(RequestFactory)),
        new ApiRouteErrorHandler(
          ApiContainer.get("options"),
          config.APP_DEBUG,
          ApiContainer.get(ExceptionSerializer),
          ApiContainer.get(RequestFactory)
        )
      ])
);
ApiContainer.set(
  ErrorLoggerServerPropsMiddleware,
  new ErrorLoggerServerPropsMiddleware(ApiContainer.get("options"), ApiContainer.get(RequestFactory))
);
ApiContainer.set(
  CompositeHeadersProvider,
  new CompositeHeadersProvider([
    ApiContainer.get(OAuthBearerHeaderProvider),
    ApiContainer.get(ApplicationHeadersProvider)
  ])
);
ApiContainer.set(
  "operationsClient",
  anHttpClient()
    .withBaseUrl(config.OPERATIONS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "opportunityClient",
  anHttpClient()
    .withBaseUrl(config.OPPORTUNITIES_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([
        ApiContainer.get(OAuthBearerHeaderProvider),
        ApiContainer.get(ApplicationHeadersProvider)
      ])
    )
    .build()
);
ApiContainer.set(
  "contentSubmissionClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SUBMISSION_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "contentScanningClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SCANNING_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "metadataClient",
  anHttpClient()
    .withBaseUrl(config.METADATA_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "communicationsClient",
  anHttpClient()
    .withBaseUrl(config.COMMUNICATIONS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "contentManagementClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_MANAGEMENT_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "legalClient",
  anHttpClient()
    .withBaseUrl(config.LEGAL_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "paymentClient",
  anHttpClient()
    .withBaseUrl(config.PAYMENTS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set("discordAccount", ApiContainer.get(DiscordAccountHttpClient));
ApiContainer.set("connectedAccounts", ApiContainer.get(ConnectedAccountsHttpClient));
// ApiContainer.set(
//   ConnectYouTubeAccountController,
//   new ConnectYouTubeAccountController(ApiContainer.get("connectedAccounts"), config.YOUTUBE_CLIENT_REDIRECT_URL)
// );
// ApiContainer.set(
//   ConnectTikTokAccountController,
//   new ConnectTikTokAccountController(ApiContainer.get("connectedAccounts"), config.TIKTOK_CLIENT_REDIRECT_URL)
// );
// ApiContainer.set(
//   ConnectTwitchAccountController,
//   new ConnectTwitchAccountController(ApiContainer.get("connectedAccounts"), config.TWITCH_CLIENT_REDIRECT_URL)
// );
// ApiContainer.set(
//   ConnectInstagramAccountController,
//   new ConnectInstagramAccountController(ApiContainer.get("connectedAccounts"), config.INSTAGRAM_CLIENT_REDIRECT_URL)
// );
// ApiContainer.set(
//   SaveFacebookPagesController,
//   new SaveFacebookPagesController(ApiContainer.get("connectedAccounts"), config.FACEBOOK_CLIENT_REDIRECT_URL)
// );
// ApiContainer.set(
//   ConnectDiscordAccountController,
//   new ConnectDiscordAccountController(ApiContainer.get("discordAccount"), config.DISCORD_CLIENT_REDIRECT_URL)
// );
ApiContainer.set(
  RedirectToLoginController,
  new RedirectToLoginController(ApiContainer.get("options"), config.LOGIN_URL)
);
ApiContainer.set(CloseSessionController, new CloseSessionController(ApiContainer.get("options")));
ApiContainer.set(CreatorsHttpClient, new CreatorsHttpClient(ApiContainer.get("operationsClient")));
ApiContainer.set(
  AuthenticatorPlugin,
  new AuthenticatorPlugin(
    new Authenticator(
      ApiContainer.get("options"),
      new PlayerProfilesHttpClient(ApiContainer.get("operationsClient"), config.LOGIN_REDIRECT_URI),
      ApiContainer.get(CreatorsHttpClient)
    )
  )
);
ApiContainer.set(
  SuccessfulAuthenticationPlugin,
  new SuccessfulAuthenticationPlugin(ApiContainer.get("options"), ApiContainer.get(CreatorsHttpClient))
);
ApiContainer.set(DisabledAccountPlugin, new DisabledAccountPlugin(ApiContainer.get("options")));
ApiContainer.set(InactiveAccountPlugin, new InactiveAccountPlugin(ApiContainer.get("options")));
ApiContainer.set("statuses", new TermsAndConditionsStatusesHttpClient(ApiContainer.get("legalClient")));
ApiContainer.set(
  UpToDateTermsAndConditionsPlugin,
  new UpToDateTermsAndConditionsPlugin(ApiContainer.get("options"), ApiContainer.get("statuses"))
);
const plugins: AuthenticationPlugin[] = [ApiContainer.get(AuthenticatorPlugin)];
if (config.FLAG_INITIAL_MESSAGE) plugins.push(new ShowInitialMessagePlugin(ApiContainer.get("options")));
ApiContainer.set(UnderageRequestToJoinPlugin, new UnderageRequestToJoinPlugin(ApiContainer.get("options")));
plugins.push(ApiContainer.get(UnderageRequestToJoinPlugin));
plugins.push(ApiContainer.get(UpToDateTermsAndConditionsPlugin));
plugins.push(ApiContainer.get(SuccessfulAuthenticationPlugin));
plugins.push(ApiContainer.get(DisabledAccountPlugin));
plugins.push(ApiContainer.get(InactiveAccountPlugin));
ApiContainer.set(StartRequestToJoinPlugin, new StartRequestToJoinPlugin(ApiContainer.get("options")));
ApiContainer.set(
  ContinueRequestToJoinPlugin,
  new ContinueRequestToJoinPlugin(
    ApiContainer.get("options"),
    new RequestsToJoinHttpClient(ApiContainer.get("operationsClient"))
  )
);
plugins.push(ApiContainer.get(ContinueRequestToJoinPlugin));
plugins.push(ApiContainer.get(StartRequestToJoinPlugin));
ApiContainer.set(ContinueOnboardingPlugin, new ContinueOnboardingPlugin(ApiContainer.get("options")));
ApiContainer.set(
  StartOnboardingPlugin,
  new StartOnboardingPlugin(
    ApiContainer.get("options"),
    new RegistrationCodesHttpClient(ApiContainer.get("operationsClient"))
  )
);
plugins.push(ApiContainer.get(ContinueOnboardingPlugin));
plugins.push(ApiContainer.get(StartOnboardingPlugin));
ApiContainer.set(NoAccountPlugin, new NoAccountPlugin(ApiContainer.get("options")));
plugins.push(ApiContainer.get(NoAccountPlugin));
ApiContainer.set(CompositeAuthenticator, new CompositeAuthenticator(plugins, ApiContainer.get(ActivityFeed)));
ApiContainer.set(
  AuthenticateController,
  new AuthenticateController(ApiContainer.get("options"), ApiContainer.get(CompositeAuthenticator))
);
ApiContainer.set(RedirectToLogoutController, new RedirectToLogoutController(config.LOGOUT_URL));
ApiContainer.set(
  SaveRegistrationCodeToSessionController,
  new SaveRegistrationCodeToSessionController(ApiContainer.get("options"))
);
ApiContainer.set("authenticationProvider", ApiContainer.get(EAAuthenticationProvider));
ApiContainer.set("creators", ApiContainer.get(LegacyCreatorsHttpClient));
ApiContainer.set("creatorsWithCreatorPrograms", ApiContainer.get(CreatorsWithProgramHttpClient));
ApiContainer.set(
  AuthenticateCreatorAction,
  new AuthenticateCreatorAction(
    ApiContainer.get("authenticationProvider"),
    ApiContainer.get("creators"),
    config.LOGIN_REDIRECT_URI,
    ApiContainer.get("creatorsWithCreatorPrograms")
  )
);
ApiContainer.set("creatorsWithFlaggedStatus", ApiContainer.get(SearchCreatorsWithFlaggedStatusHttpClient));
ApiContainer.set(
  AuthenticateCreatorWithFlaggedStatusAction,
  new AuthenticateCreatorWithFlaggedStatusAction(
    ApiContainer.get("authenticationProvider"),
    ApiContainer.get("creatorsWithFlaggedStatus"),
    config.LOGIN_REDIRECT_URI,
    ApiContainer.get("creatorsWithCreatorPrograms")
  )
);
ApiContainer.set("discordAccount", ApiContainer.get(DiscordAccountHttpClient));
ApiContainer.set("operations", ApiContainer.get(LegacyOpportunitiesHttpClient));
ApiContainer.set("communications", ApiContainer.get(EmailsHttpClient));
ApiContainer.set("opportunities", ApiContainer.get(OpportunitiesHttpClient));
ApiContainer.set("registrations", ApiContainer.get(ValidRegistrationCodesHttpClient));
ApiContainer.set("termsAndConditions", ApiContainer.get(TermsAndConditionsHttpClient));
ApiContainer.set("cachedTermsAndConditions", ApiContainer.get(CachedTermsAndConditions));
ApiContainer.set("paymentInformation", ApiContainer.get(PaymentInformationHttpClient));
ApiContainer.set("opportunityRegistrations", ApiContainer.get(OpportunityRegistrationsHttpClient));
ApiContainer.set("participations", ApiContainer.get(ParticipationsHttpClient));
ApiContainer.set("notifications", ApiContainer.get(NotificationsHttpClient));
ApiContainer.set("contentManagement", ApiContainer.get(MicroCopiesHttpClient));
ApiContainer.set("pageMappers", {
  notifications: [new NotificationsPageMapper(), new NotificationsBellMapper()],
  noAccount: [new NoAccountPageMapper(), new CommonPageMapper(), new ApplicationStartPageMapper()],
  ageRestriction: [new AgeRestrictionPageMapper(), new CommonPageMapper()],
  applicationAccepted: [new ApplicationAcceptedPageMapper(), new CommonPageMapper()],
  applicationPending: [new ApplicationPendingPageMapper(), new ApplicationRejectedPageMapper(), new CommonPageMapper()],
  applicationComplete: [new ApplicationCompletePageMapper(), new CommonPageMapper()],
  applicationRejected: [new ApplicationRejectedPageMapper(), new CommonPageMapper()],
  applicationStart: [new ApplicationStartPageMapper(), new CommonPageMapper(), new InformationPageMapper()],
  information: [
    new InformationPageMapper(),
    new CommonPageMapper(),
    new BreadcrumbPageMapper(),
    new CommunicationPreferencesPageMapper(),
    new AddContentPageMapper(),
    new ConnectAccountsPageMapper()
  ],
  creatorType: [
    new CreatorTypePageMapper(),
    new CommonPageMapper(),
    new BreadcrumbPageMapper(),
    new InformationPageMapper()
  ],
  franchisesYouPlay: [
    new FranchisesYouPlayPageMapper(),
    new CommonPageMapper(),
    new BreadcrumbPageMapper(),
    new InformationPageMapper()
  ]
});
ApiContainer.set(
  ContentManagementService,
  new ContentManagementService(
    ApiContainer.get(MicroCopiesHttpClient),
    ApiContainer.get("pageMappers"),
    config.pagesMicroCopy,
    config.PROGRAM_CODE
  )
);
ApiContainer.set(StartApplicationController, new StartApplicationController(ApiContainer.get("options")));
ApiContainer.set(
  VerifyAccessToProgramServerPropsMiddleware,
  new VerifyAccessToProgramServerPropsMiddleware(ApiContainer.get("options"))
);
ApiContainer.set(
  VerifyIncompleteRegistrationPropsMiddleware,
  new VerifyIncompleteRegistrationPropsMiddleware(ApiContainer.get("options"))
);
ApiContainer.set(
  VerifyRequestToJoinServerPropsMiddleware,
  new VerifyRequestToJoinServerPropsMiddleware(ApiContainer.get("options"))
);

export default ApiContainer;
