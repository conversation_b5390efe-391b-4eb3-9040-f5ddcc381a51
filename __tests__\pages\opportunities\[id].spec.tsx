import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import {
  aOpportunityWithPerks,
  aOpportunityWithPerksAndContentSubmissionWithDeliverables
} from "../../factories/opportunities/OpportunityWithPerks";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../../helpers/window";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { renderWithToast } from "../../helpers/toast";
import { useAppContext } from "@src/context";
import { aCreatorWithCreatorCode } from "../../factories/creators/CreatorWithCreatorCode";
import Opportunity from "../../../pages/opportunities/[id]";
import layout from "../../translations/common";
import { aDeliverable } from "../../factories/deliverable/Deliverable";
import { aContentSubmissionWithDeliverables } from "../../factories/opportunities/OpportunityContentSubmissionWithDeliverables";
import { aSubmittedContentWithDeliverable } from "../../factories/opportunities/SubmittedContentWithDeliverable";
import SubmittedContentService, {
  SubmittedContentWithDeliverableDetail
} from "@src/api/services/SubmittedContentService";
import { ContentsWithDeliverable } from "@src/submittedContent/SubmittedContentHttpClient";
import OpportunityService, {
  OpportunityWithDeliverables,
  OpportunityWithPerks
} from "@src/api/services/OpportunityService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/api/services/OpportunityService", () => {
  return {
    ...jest.requireActual("../../../src/api/services/OpportunityService"),
    getOpportunityWithDeliverables: jest.fn(),
    getParticipationDetails: jest.fn()
  };
});
jest.mock("../../../src/api/services/SubmittedContentService", () => {
  return {
    ...jest.requireActual("../../../src/api/services/SubmittedContentService"),
    getSubmittedContents: jest.fn(),
    getSubmittedContentsFinalRemarks: jest.fn()
  };
});
jest.mock("react-toastify", () => {
  const originalModule = jest.requireActual("react-toastify");
  return {
    ...originalModule,
    toast: {
      ...originalModule.toast,
      dismiss: jest.fn()
    }
  };
});
jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../analytics/browser/src/ampli");
jest.mock("../../../src/analytics/BrowserAnalytics");

describe("Opportunity", () => {
  mockMatchMedia();
  const user = { id: "GAURAV123" };
  const opportunityProps = {
    user,
    WATERMARKS_URL: "",
    YOUTUBE_HOSTS: ["youtube.com", "m.youtube.com"],
    TWITCH_HOSTS: ["twitch.tv"],
    INSTAGRAM_HOSTS: ["instagram.com"],
    FACEBOOK_HOSTS: ["facebook.com"],
    TIKTOK_HOSTS: ["tiktok.com"],
    accountConnected: "",
    error: null,
    pages: [],
    referer: "",
    invalidTikTokScope: false,
    UPDATE_OPPORTUNITY_DETAILS: false,
    FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED: false,
    FLAG_NEW_FOOTER_ENABLED: false,
    FLAG_NEW_NAVIGATION_ENABLED: false,
    FLAG_CONTENT_WITH_FINAL_REMARK: false
  };
  const opportunity = new OpportunityWithPerks(
    aOpportunityWithPerks({
      hasDeliverables: true,
      registrationPeriod: { endDate: LocalizedDate.epochPlusDays(5) },
      contentSubmission: {
        socialMedia: true,
        submissionWindow: {
          endDate: LocalizedDate.epochPlusMonths(2),
          timeZone: "GMT"
        }
      }
    })
  );
  const analytics = {
    viewedOpportunityDetails: jest.fn(),
    startedContentSubmissionFlow: jest.fn(),
    clickedDeliverablesTab: jest.fn(),
    acceptedOpportunityInvitation: jest.fn(),
    clickedPaymentSettingsTab: jest.fn(),
    clickedPaymentDetailsIncompleteHelperBanner: jest.fn(),
    declinedOpportunityInvitation: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      metadataClient: {},
      errorHandler: jest.fn(),
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getContentTypes: jest.fn().mockResolvedValue([{ value: "video", label: "Video" }])
    });
    (useRouter as jest.Mock).mockImplementation(() => ({
      isReady: true,
      query: { id: "OPPO123" },
      locale: "en-us"
    }));
  });

  it("removes the toast when leaving the page for deliverables", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { showWarningForChangesRequested: false }
    });
    const opportunityWithDeliverables = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        ...opportunity,
        hasDeliverables: true,
        hasGameCodes: false,

        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              id: "1234",
              type: "SINGLE",
              format: "WEBSITE",
              socialAccountTypes: null,
              status: "CHANGE_REQUESTED"
            }),
            aDeliverable({
              id: "7890",
              type: "SINGLE",
              format: "WEBSITE",
              socialAccountTypes: null,
              status: "CHANGE_REQUESTED"
            })
          ]
        })
      })
    );
    (OpportunityService.getOpportunityWithDeliverables as jest.Mock).mockResolvedValue({
      data: { opportunity: opportunityWithDeliverables, creator: aCreatorWithCreatorCode({ id: user.id }) }
    });
    const participationStatusResponse = [{ participationId: "12", status: "JOINED", id: "OPPO123" }];
    (OpportunityService.getParticipationDetails as jest.Mock).mockResolvedValue({
      data: participationStatusResponse
    });
    const submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "fifa"
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "sims"
          }) as unknown as ContentsWithDeliverable
        )
      ],
      count: 2,
      total: 2
    };
    (SubmittedContentService.getSubmittedContents as jest.Mock).mockResolvedValue({
      data: submittedContent
    });
    const { unmount } = renderWithToast(<Opportunity {...opportunityProps} />);
    const overViewTab = await screen.findByText("opportunities:overview");
    const contentTab = await screen.findByText("opportunities:contentDeliverables");
    await userEvent.click(contentTab);
    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent("content-submission:changesRequiredTitle");
    await userEvent.click(overViewTab);
    expect(toastContainer).toBeInTheDocument();

    unmount();

    await waitFor(() => {
      expect(toast.dismiss).toHaveBeenCalledTimes(1);
      expect(toast.dismiss).toHaveBeenCalledWith("SHOW_WARNING_FOR_CHANGES_REQUESTED");
    });
  });

  it("shows error toast when error occurred in submitted content count", async () => {
    jest.useFakeTimers();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { isToastError: true, isError: layout.main.unhandledError }
    });
    const errorResponse = {
      code: "view-aggregate-unknown-aggregate",
      message: "Cannot complete the action because {error message}",
      title: "Not Found",
      type: "https://www.w3.org//Protocols//rfc2616//rfc2616-sec10.html#sec10.4.5",
      status: 404
    };
    (OpportunityService.getOpportunityWithDeliverables as jest.Mock).mockResolvedValue({
      data: { opportunity }
    });
    const participationStatusResponse = [{ participationId: "12", status: "JOINED", id: "OPPO123" }];
    (OpportunityService.getParticipationDetails as jest.Mock).mockResolvedValue({
      data: participationStatusResponse
    });
    (SubmittedContentService.getSubmittedContents as jest.Mock).mockRejectedValue({
      data: errorResponse
    });

    const { unmount } = renderWithToast(<Opportunity {...opportunityProps} />);

    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent(/unhandledError/i);
    expect(toastContainer).toBeInTheDocument();
    unmount();
    await waitFor(() => expect(toast.dismiss).toHaveBeenCalledTimes(1));
    jest.useRealTimers();
  });

  it("shows error toast when error occurred while getting submitted content for deliverables", async () => {
    jest.useFakeTimers();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { isToastError: true, isError: layout.main.unhandledError }
    });
    const opportunityWithDeliverables = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        ...opportunity,
        hasDeliverables: true,

        contentSubmission: new SubmittedContentWithDeliverableDetail(
          aContentSubmissionWithDeliverables({
            deliverables: [aDeliverable()]
          }) as unknown as ContentsWithDeliverable
        )
      })
    );
    const errorResponse = {
      code: "view-aggregate-unknown-aggregate",
      message: "Cannot complete the action because {error message}",
      title: "Not Found",
      type: "https://www.w3.org//Protocols//rfc2616//rfc2616-sec10.html#sec10.4.5",
      status: 404
    };
    (OpportunityService.getOpportunityWithDeliverables as jest.Mock).mockResolvedValue({
      data: { opportunity: opportunityWithDeliverables }
    });
    const participationStatusResponse = [{ participationId: "12", status: "JOINED", id: "OPPO123" }];
    (OpportunityService.getParticipationDetails as jest.Mock).mockResolvedValue({
      data: participationStatusResponse
    });
    (SubmittedContentService.getSubmittedContents as jest.Mock).mockRejectedValue({
      data: errorResponse
    });

    const { unmount } = renderWithToast(<Opportunity {...opportunityProps} />);

    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent(/unhandledError/i);
    expect(toastContainer).toBeInTheDocument();
    unmount();
    await waitFor(() => expect(toast.dismiss).toHaveBeenCalledTimes(1));
    jest.useRealTimers();
  });

  describe("with FLAG_CONTENT_WITH_FINAL_REMARK enabled", () => {
    it("shows submitted content with final remarks", async () => {
      const opportunityWithDeliverables = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          ...opportunity,
          hasDeliverables: true,
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [aDeliverable()]
          })
        })
      );
      (OpportunityService.getOpportunityWithDeliverables as jest.Mock).mockResolvedValue({
        data: { opportunity: opportunityWithDeliverables }
      });
      const participationStatusResponse = [{ participationId: "12", status: "JOINED", id: "OPPO123" }];
      (OpportunityService.getParticipationDetails as jest.Mock).mockResolvedValue({
        data: participationStatusResponse
      });
      const submittedContent = {
        contents: [
          {
            id: "content1",
            name: "Content with remarks",
            status: "APPROVED",
            contentType: "video",
            deliverableId: "del1",
            reviewFinalRemark: {
              content: "Final review remarks",
              author: "Reviewer",
              date: new Date("2025-03-05").getTime()
            }
          }
        ],
        count: 1,
        total: 1
      };
      (SubmittedContentService.getSubmittedContentsFinalRemarks as jest.Mock).mockResolvedValue({
        data: submittedContent
      });

      renderWithToast(<Opportunity {...opportunityProps} FLAG_CONTENT_WITH_FINAL_REMARK={true} />);

      await waitFor(() => {
        expect(SubmittedContentService.getSubmittedContents).not.toHaveBeenCalled();
        expect(SubmittedContentService.getSubmittedContentsFinalRemarks).toHaveBeenCalledTimes(1);
      });
    });
  });
});
