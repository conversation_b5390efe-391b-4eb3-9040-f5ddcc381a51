import "reflect-metadata";
import { render, screen, waitFor, within } from "@testing-library/react";
import ContentDeliverablesTab, { Size } from "../../../components/pages/content-submission/ContentDeliverablesTab";
import {
  commonTranslations,
  labels,
  myContentTranslations,
  submitFileContentTranslations,
  submitWebsiteContentTranslations
} from "../../translations";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../factories/opportunities/OpportunityWithPerks";
import { OpportunityWithDeliverables } from "@src/api/services/OpportunityService";
import { aContentSubmissionWithDeliverables } from "../../factories/opportunities/OpportunityContentSubmissionWithDeliverables";
import opportunitiesLabels from "../../translations/opportunities";
import { aConnectedAccount } from "../../factories/creators/ConnectedAccounts";
import { aCreatorWithExpiredAccounts } from "../../factories/creators/CreatorWithExpiredAccount";
import userEvent from "@testing-library/user-event";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { renderWithToast } from "../../helpers/toast";
import { WINDOW_PARAMS } from "../../../utils";
import {
  INSTA_CANNOT_CONNECT,
  INSTA_WARNING_ERROR,
  YOUTUBE_NO_CHANNEL_ERROR
} from "pages/interested-creators/information";
import { aDeliverable } from "../../factories/deliverable/Deliverable";
import SubmittedContentService, {
  SubmittedContentWithDeliverableDetail,
  SubmittedContentWithFinalRemark
} from "@src/api/services/SubmittedContentService";
import { aSubmittedContentWithDeliverable } from "../../factories/opportunities/SubmittedContentWithDeliverable";
import { aSubmittedContentWithFinalRemark } from "../../factories/opportunities/SubmittedContentWithReviewFinalRemark";
import {
  ContentsWithDeliverable,
  ContentsWithReviewFinalRemark
} from "@src/submittedContent/SubmittedContentHttpClient";
import { aContentFeedback } from "../../factories/opportunities/ContentFeedback";
import { mockLocalStorage } from "../../helpers/window";
import { useAppContext } from "@src/context";
import { useRouter } from "next/router";
import "next/config";
import CreatorWithExpiredAccounts from "@src/creators/CreatorWithExpiredAccounts";
import ConnectedAccountsService from "@src/api/services/ConnectedAccountsService";
import { delay } from "__tests__/helpers/timer";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../analytics/browser/src/ampli");
jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/api/services/SubmittedContentService", () => {
  return {
    ...jest.requireActual("../../../src/api/services/SubmittedContentService"),
    validateContent: jest.fn(),
    submitWebsiteContent: jest.fn(),
    getContentsFeedback: jest.fn(),
    submitWebsiteContentDeliverable: jest.fn(),
    getSubmittedContents: jest.fn()
  };
});
jest.mock("../../../src/api/services/ConnectedAccountsService");

const addAccountsWithTiktok = [
  ["YOUTUBE", "/api/youtube-login"],
  ["FACEBOOK", "/api/facebook-login"],
  ["INSTAGRAM", "/api/instagram-login"],
  ["TWITCH", "/api/twitch-login"],
  ["TIKTOK", "/api/tiktok-login"]
];

describe("ContentDeliverablesTab", () => {
  const router = { locale: "en-us", push: jest.fn() };
  Object.defineProperty(window, "location", {
    configurable: true,
    writable: true,
    value: { reload: jest.fn() }
  });
  const { contentSubmissionTabLabels } = labels;
  const opportunity = new OpportunityWithDeliverables(aOpportunityWithPerksAndContentSubmissionWithDeliverables());
  const contentDeliverablesTabProps = {
    WATERMARKS_URL: "",
    contentDeliverablesTabLabels: contentSubmissionTabLabels,
    layout: commonTranslations,
    opportunity,
    isJoined: false,
    isSubmissionWindowClosed: false,
    hasContentSubmissionWindowStarted: true,
    size: "DESKTOP" as Size,
    opportunitiesLabels,
    creator: CreatorWithExpiredAccounts.fromApi(
      aCreatorWithExpiredAccounts({
        connectedAccounts: [aConnectedAccount({ username: "Hari70a", type: "YOUTUBE", isExpired: false })]
      })
    ),
    accountConnected: null,
    t: jest.fn((str) => str),
    locale: "en-us",
    analytics: {} as unknown as BrowserAnalytics,
    setConnectedAccount: jest.fn(),
    pages: [],
    error: null,
    participationId: "",
    YOUTUBE_HOSTS: ["youtube.com", "m.youtube.com"],
    TWITCH_HOSTS: ["twitch.tv"],
    INSTAGRAM_HOSTS: ["instagram.com"],
    FACEBOOK_HOSTS: ["facebook.com"],
    TIKTOK_HOSTS: ["tiktok.com"],
    setUpdatedContent: jest.fn(),
    hasDeliverables: true,
    thumbnail: "",
    myContentLabels: myContentTranslations,
    viewAllLabel: "",
    deliverablePages: null
  };
  let localStorage;

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      metadataClient: {},
      errorHandler: jest.fn(),
      configuration: {
        FLAG_SIGNED_URL_V1_ENABLED: false,
        FLAG_CONTENT_WITH_FINAL_REMARK: false
      }
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getContentTypes: jest.fn().mockResolvedValue([{ value: "video", label: "Video" }])
    });
    localStorage = mockLocalStorage();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows available resource section", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasPayments: false,
        hasAttachments: true,
        attachmentsUrl: "http://testUrl.zip"
      })
    );

    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} />);

    expect(
      await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.availableResources)
    ).toBeInTheDocument();
    expect(screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.downloadEaLogo)).toBeInTheDocument();
    expect(
      screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.downloadAttachments)
    ).toBeInTheDocument();
    expect(
      screen.getAllByRole("link", { name: contentSubmissionTabLabels.contentSubmissionLabels.downloadAttachments })[0]
    ).toHaveAttribute("href", "http://testUrl.zip");
  });

  it("shows Content deliverables section for before joining an opportunity", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE"]
            }),
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"]
            })
          ]
        })
      })
    );

    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} />);

    await waitFor(() => {
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.contentDeliverables)
      ).toBeInTheDocument();
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.deliverablesInstruction)
      ).toBeInTheDocument();
      expect(screen.getAllByTestId("deliverable-card-container")).toHaveLength(2);
    });
  });

  it("hides content deliverables instruction & deliverable status badge when submission window is closed and creator hasn't joined the opportunity", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasPayments: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ socialAccountTypes: ["YOUTUBE"] })]
        })
      })
    );

    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        isSubmissionWindowClosed={true}
        opportunity={opportunity}
      />
    );

    await waitFor(() => {
      expect(
        screen.queryByText(contentSubmissionTabLabels.contentSubmissionLabels.deliverablesInstruction)
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText(contentSubmissionTabLabels.contentSubmissionLabels.submissionWindowClosed)
      ).not.toBeInTheDocument();
      expect(screen.getByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /Submit/i })).not.toBeInTheDocument();
    });
  });

  describe("when creator has joined the opportunity", () => {
    it("shows content deliverables section in 'Not yet submitted' state", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                socialAccountTypes: ["YOUTUBE"],
                id: "test123",
                type: "SINGLE"
              })
            ]
          })
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      await waitFor(() => {
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.contentDeliverables)
        ).toBeInTheDocument();
        expect(
          screen.queryByText(contentSubmissionTabLabels.contentSubmissionLabels.deliverablesInstruction)
        ).not.toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.notYetSubmitted)
        ).toBeInTheDocument();
        expect(screen.getAllByTestId("deliverable-card-container")).toHaveLength(1);
        expect(screen.queryByTestId("deliverable-content-card")).not.toBeInTheDocument(); // In not yet submitted state, there is no content, so added this assertion
      });
    });

    it("shows content deliverables & its message when submission window is yet to start", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                socialAccountTypes: ["YOUTUBE"],
                format: "SOCIAL"
              })
            ]
          })
        })
      );

      render(
        <ContentDeliverablesTab
          {...contentDeliverablesTabProps}
          opportunity={opportunity}
          isJoined
          hasContentSubmissionWindowStarted={false}
        />
      );

      expect(
        await screen.findByRole("heading", {
          name: contentSubmissionTabLabels.contentSubmissionLabels.contentDeliverables
        })
      ).toBeInTheDocument();
      expect(
        await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.submissionOpensWithNoContent)
      ).toBeInTheDocument();
      expect(await screen.findByTestId("deliverable-card-container")).toBeInTheDocument();
    });

    it("shows content guidelines section", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            guidelines: "<p>Madden NFL is an American football video</p>"
          })
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.contentGuidelinesTitle)).toBeInTheDocument();
        expect(screen.getByText("Madden NFL is an American football video")).toBeInTheDocument();
      });
    });

    it("shows disclosure policy section when opportunity has payments", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: true
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      await waitFor(() => {
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.paid.contentGuideLineDescription1)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.paid.contentGuideLineDescription2)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.paid.contentGuideLineDescription3)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.paid.contentGuideLineDescription4)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.paid.contentGuideLineDescription5)
        ).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description1")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description2")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description3")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description4")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description5")).toBeInTheDocument();
      });
    });

    it("shows disclosure policy section when opportunity has no payments", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: false
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      await waitFor(() => {
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.nonPaid.contentGuideLineDescription1)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.nonPaid.contentGuideLineDescription2)
        ).toBeInTheDocument();
        expect(
          screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.nonPaid.contentGuideLineDescription3)
        ).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description1")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description2")).toBeInTheDocument();
        expect(screen.getByTestId("content-guide-line-icon-description3")).toBeInTheDocument();
      });
    });

    it("shows link submission section when opportunity has payments", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: true
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      expect(
        await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.linkSubmissionGuideLine)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(
          contentSubmissionTabLabels.contentSubmissionLabels.paid.linkSubmissionGuideLineSubTitle1
        )
      ).toBeInTheDocument();
      expect(
        await screen.findByText(
          contentSubmissionTabLabels.contentSubmissionLabels.paid.linkSubmissionGuideLineSubTitle2
        )
      ).toBeInTheDocument();
    });

    it("shows link submission section when opportunity has no payments", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: false
        })
      );

      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      expect(
        await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.linkSubmissionGuideLine)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(
          contentSubmissionTabLabels.contentSubmissionLabels.nonPaid.linkSubmissionGuideLineSubTitle1
        )
      ).toBeInTheDocument();
      expect(
        await screen.findByText(
          contentSubmissionTabLabels.contentSubmissionLabels.nonPaid.linkSubmissionGuideLineSubTitle2
        )
      ).toBeInTheDocument();
    });

    it("shows modal for website submission", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "WEBSITE",
                socialAccountTypes: null
              })
            ]
          })
        })
      );
      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);
      await userEvent.click(await screen.findByRole("button", { name: /submit/i }));

      const { getByLabelText, getByRole } = within(await screen.findByRole("dialog"));
      expect(getByLabelText("Content Title")).toBeInTheDocument();
      expect(getByLabelText("Please enter the Content URL:")).toBeInTheDocument();
      expect(getByLabelText("Content Type")).toBeInTheDocument();
      expect(getByRole("button", { name: /close/i })).toBeInTheDocument();
      expect(getByRole("button", { name: /cancel/i })).toBeInTheDocument();
      expect(getByRole("button", { name: /Submit$/i })).toBeInTheDocument();
      expect(getByRole("heading", { name: /Add content from a Website$/i })).toBeInTheDocument();
    });

    it("hides modal for website submission on closing it", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "WEBSITE",
                socialAccountTypes: null
              })
            ]
          })
        })
      );
      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);
      await userEvent.click(await screen.findByRole("button", { name: /submit/i }));
      const { getByRole } = within(await screen.findByRole("dialog"));

      await userEvent.click(getByRole("button", { name: /close/i }));

      await waitFor(() => {
        expect(screen.queryByRole("heading", { name: /Add content from a Website$/i })).not.toBeInTheDocument();
        expect(screen.queryByLabelText("Content Title")).not.toBeInTheDocument();
        expect(screen.queryByLabelText("Please enter the Content URL:")).not.toBeInTheDocument();
        expect(screen.queryByLabelText("Content Type")).not.toBeInTheDocument();
      });
    });

    it("shows modal for file upload", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "FILE",
                socialAccountTypes: null,
                uploadFileTypes: [{ type: "text", extensions: ["TXT"] }]
              })
            ]
          })
        })
      );
      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

      await userEvent.click(await screen.findByRole("button", { name: /submit/i }));

      const { getByLabelText, getByRole, getByText } = within(await screen.findByRole("dialog"));
      expect(getByLabelText("Content Title")).toBeInTheDocument();
      expect(getByLabelText("Content Type")).toBeInTheDocument();
      expect(getByText("File Upload")).toBeInTheDocument();
      expect(getByRole("button", { name: /close/i })).toBeInTheDocument();
      expect(getByRole("button", { name: /cancel/i })).toBeInTheDocument();
      expect(getByRole("button", { name: /upload$/i })).toBeInTheDocument();
      expect(getByRole("heading", { name: /Upload a file from your device$/i })).toBeInTheDocument();
    });

    it("hides modal for file submission on closing it", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "FILE",
                socialAccountTypes: null,
                uploadFileTypes: [{ type: "text", extensions: ["TXT"] }]
              })
            ]
          })
        })
      );
      render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);
      await userEvent.click(await screen.findByRole("button", { name: /submit/i }));
      const { getByRole } = within(await screen.findByRole("dialog"));

      await userEvent.click(getByRole("button", { name: /close/i }));

      await waitFor(() => {
        expect(screen.queryByRole("heading", { name: /Upload a file from your device$/i })).not.toBeInTheDocument();
        expect(screen.queryByLabelText("Content Title")).not.toBeInTheDocument();
        expect(screen.queryByLabelText("Content Type")).not.toBeInTheDocument();
        expect(screen.queryByText("File Upload")).not.toBeInTheDocument();
      });
    });

    it("shows update button for change requested deliverable card for file upload", async () => {
      const deliverableId = "test-123";
      const content = {
        [deliverableId]: [
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
          )
        ]
      };
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "FILE",
                socialAccountTypes: null,
                type: "SINGLE",
                status: "CHANGE_REQUESTED",
                id: deliverableId
              })
            ]
          })
        })
      );

      renderWithToast(
        <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
      );

      expect(await screen.findByRole("button", { name: commonTranslations.buttons.update })).toBeInTheDocument();
    });

    it("shows the feedback for a content for submitted file content", async () => {
      const contentsFeedbackResponse = {
        contentsFeedback: [
          aContentFeedback({
            ...aContentFeedback(),
            formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
          })
        ]
      };
      const deliverableId = "test-123";
      const content = {
        [deliverableId]: [
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
          )
        ]
      };
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "FILE",
                socialAccountTypes: null,
                type: "SINGLE",
                status: "CHANGE_REQUESTED",
                id: deliverableId
              })
            ]
          })
        })
      );
      (SubmittedContentService.getContentsFeedback as jest.Mock).mockResolvedValue({
        data: contentsFeedbackResponse
      });
      renderWithToast(
        <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
      );

      await userEvent.click(await screen.findByRole("button", { name: /View Changes Required$/ }));

      await waitFor(() => {
        expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
        expect(screen.getByRole("button", { name: /Hide Changes Required$/ })).toBeInTheDocument();
      });
    });

    it("shows the file upload modal on updating the content, when the type is FILE", async () => {
      const contentsFeedbackResponse = {
        contentsFeedback: [
          aContentFeedback({
            ...aContentFeedback(),
            formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
          })
        ]
      };
      const deliverableId = "test-123";
      const content = {
        [deliverableId]: [
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
          )
        ]
      };
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "FILE",
                socialAccountTypes: null,
                type: "SINGLE",
                status: "CHANGE_REQUESTED",
                id: deliverableId,
                uploadFileTypes: [{ type: "text", extensions: ["TXT"] }]
              })
            ]
          })
        })
      );
      (SubmittedContentService.getContentsFeedback as jest.Mock).mockResolvedValue({
        data: contentsFeedbackResponse
      });
      renderWithToast(
        <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
      );
      await userEvent.click(screen.getByRole("button", { name: /View Changes Required$/ }));

      await userEvent.click(await screen.findByRole("button", { name: commonTranslations.buttons.update }));

      await waitFor(() => {
        expect(screen.getByText(submitFileContentTranslations.title)).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /Upload$/ })).toBeInTheDocument();
      });
    });

    it("shows 'Submission window closed' label in deliverable card when submission window is closed & no content submitted", async () => {
      const deliverableId = "abc123";
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [aDeliverable({ socialAccountTypes: ["FACEBOOK"], id: deliverableId })]
          })
        })
      );
      const content = {
        [deliverableId]: []
      };

      render(
        <ContentDeliverablesTab
          {...contentDeliverablesTabProps}
          isSubmissionWindowClosed={true}
          opportunity={opportunity}
          isJoined
          content={content}
        />
      );

      expect(await screen.findByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(
        await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.submissionWindowClosed)
      ).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /Submit/i })).not.toBeInTheDocument();
    });

    it("shows the deliverable status for a single submission when submission window is closed with content already submitted", async () => {
      const deliverableId = "xyz123";
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({ socialAccountTypes: ["FACEBOOK"], id: deliverableId, type: "SINGLE", status: "APPROVED" })
            ]
          })
        })
      );
      const content = {
        [deliverableId]: [
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable() as unknown as ContentsWithDeliverable
          )
        ]
      };

      render(
        <ContentDeliverablesTab
          {...contentDeliverablesTabProps}
          isSubmissionWindowClosed={true}
          opportunity={opportunity}
          isJoined
          content={content}
        />
      );

      expect(await screen.findByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.completed)).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /Submit/i })).not.toBeInTheDocument();
    });

    it("shows 'Submission window closed' label for a 'UNLIMITED' submission when submission window is closed with content already submitted", async () => {
      const deliverableId = "xyz123";
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [aDeliverable({ socialAccountTypes: ["FACEBOOK"], id: deliverableId, type: "UNLIMITED" })]
          })
        })
      );
      const content = {
        [deliverableId]: [
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable() as unknown as ContentsWithDeliverable
          ),
          new SubmittedContentWithDeliverableDetail(
            aSubmittedContentWithDeliverable() as unknown as ContentsWithDeliverable
          )
        ]
      };

      render(
        <ContentDeliverablesTab
          {...contentDeliverablesTabProps}
          isSubmissionWindowClosed={true}
          opportunity={opportunity}
          isJoined
          content={content}
        />
      );

      expect(await screen.findByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(
        await screen.findByText(contentSubmissionTabLabels.contentSubmissionLabels.submissionWindowClosed)
      ).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /Submit/i })).not.toBeInTheDocument();
    });
  });

  it("shows only the connected accounts defined by the deliverable", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));

    expect(await screen.findByText(/Please select where you’d like to submit content from:/i)).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "Hari70a" })).toBeInTheDocument();
    expect(await screen.findByTestId("submit-content-button-youtube-icon")).toBeInTheDocument();
    expect(screen.queryByTestId("submit-content-button-instagram-icon")).not.toBeInTheDocument();
    expect(screen.queryByTestId("submit-content-button-facebook-icon")).not.toBeInTheDocument();
    expect(screen.queryByTestId("submit-content-button-twitch-icon")).not.toBeInTheDocument();
    expect(screen.queryByTestId("submit-content-button-tiktok-icon")).not.toBeInTheDocument();
  });

  it("shows the success toast when an account is successfully connected", async () => {
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { connectedNewSocialAccount: jest.fn() } as unknown as BrowserAnalytics;

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        isJoined
        accountConnected={"YouTube"}
        analytics={analytics}
      />
    );

    expect(await screen.findByText(/add-content:contentSubmissionSucessTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/add-content:contentSubmissionSucessDescription/i)).toBeInTheDocument();
    expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(1);
  });

  it("clears the connected account type in session after closing the success toast", async () => {
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    localStorage.setItem("deliverableTitle", "Test123");
    localStorage.setItem("deliverableType", "SINGLE");
    const analytics = { connectedNewSocialAccount: jest.fn() } as unknown as BrowserAnalytics;
    const { unmount } = renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        isJoined
        accountConnected={"YouTube"}
        analytics={analytics}
      />
    );

    // Closing the toast
    await userEvent.click(await screen.findByRole("button", { name: /Close/i }));

    await waitFor(() => {
      expect(screen.queryByRole("alert")).not.toBeInTheDocument();
      expect(analytics.connectedNewSocialAccount).toHaveBeenCalledTimes(1);
      expect(analytics.connectedNewSocialAccount).toHaveBeenCalledWith({
        accountType: "YouTube",
        locale: "en-us",
        deliverableTitle: "Test123",
        deliverableType: "SINGLE"
      });
      expect(localStorage.removeItem).toHaveBeenCalledTimes(2);
      expect(localStorage.removeItem).toHaveBeenCalledWith("deliverableTitle");
      expect(localStorage.removeItem).toHaveBeenLastCalledWith("deliverableType");
    });
    unmount(); // triggering toast's onClose
    expect(ConnectedAccountsService.clearAccountType).toHaveBeenCalledTimes(2);
  });

  it("shows name for a connected account if username is not found", async () => {
    const creator = CreatorWithExpiredAccounts.fromApi(
      aCreatorWithExpiredAccounts({
        connectedAccounts: [aConnectedAccount({ name: "Hari70a", username: "", type: "YOUTUBE", isExpired: false })]
      })
    );
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined creator={creator} />
    );

    await userEvent.click(screen.getByRole("button", { name: /submit/i }));

    expect(await screen.findByRole("button", { name: /Hari70a/i })).toBeInTheDocument();
    expect(await screen.findByText(/Please select where you’d like to submit content from:/i)).toBeInTheDocument();
  });

  it("opens Connect an Account Modal on clicking 'Connect new account button'", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE", "INSTAGRAM", "TIKTOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);
    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));

    await userEvent.click(screen.getByRole("button", { name: /Connect a new account/i }));

    const { getByText, getAllByRole, getByTestId } = within(await screen.findByRole("dialog"));
    expect(getByText(/Connect an Account/i)).toBeInTheDocument();
    expect(getAllByRole("button", { name: /Add Account/i })).toHaveLength(
      opportunity.contentSubmission.deliverables[0].socialAccountTypes.length
    );
    expect(getByTestId("add-account-button-TIKTOK")).toBeInTheDocument();
  });

  it("closes Connect an Account Modal on clicking close icon", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE", "INSTAGRAM"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);
    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));

    await userEvent.click(screen.getByRole("button", { name: /Connect a new account/i }));

    await userEvent.click(screen.getByRole("button", { name: /close/i }));

    await waitFor(() => expect(screen.queryByText("Connect an Account")).not.toBeInTheDocument());
  });

  it.each(addAccountsWithTiktok)("opens consent screen to connect a %s account", async (account, link) => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: [account],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    window.open = jest.fn().mockReturnValue({ closed: true });
    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} isJoined opportunity={opportunity} />);
    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));
    await userEvent.click(screen.getByRole("button", { name: /Connect a new account/i }));

    await userEvent.click(screen.getByTestId(`add-account-button-${account}`));

    await waitFor(() => expect(window.open).toHaveBeenCalledWith(link, "_blank", WINDOW_PARAMS));
  });

  it("shows modal with Facebook pages if present", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        pages={[
          {
            accessToken: "asdhgkfdgh",
            id: "s1556",
            name: "Hariea Test"
          }
        ]}
      />
    );

    const { getByText, getByRole } = within(await screen.findByRole("dialog"));
    // Check for modal headers
    expect(getByText("Please select a Facebook page")).toBeInTheDocument();
    //Check for fb pages radio button.
    expect(getByRole("radio", { name: "Hariea Test" })).toBeInTheDocument();
  });

  it("connects a facebook page and stay in 'Deliverables' tab", async () => {
    const url = "?tab=content-deliverables";
    Object.defineProperty(window, "location", {
      value: { href: url }
    });
    (ConnectedAccountsService.connectFbPages as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        pages={[
          {
            accessToken: "asdhgkfdgh",
            id: "s1556",
            name: "Hariea Test"
          }
        ]}
      />
    );
    const { getByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getByRole("radio", { name: "Hariea Test" }));
    await userEvent.click(getByRole("button", { name: "Connect" }));

    await waitFor(() => {
      expect(ConnectedAccountsService.connectFbPages).toHaveBeenCalledTimes(1);
      expect(window.location.href).toEqual(url);
    });
  });

  it("clears the facebook pages in session on closing the Facebook pages modal", async () => {
    (ConnectedAccountsService.clearFbPages as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    const url = "?tab=content-deliverables";
    Object.defineProperty(window, "location", {
      value: { href: url }
    });
    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        pages={[
          {
            accessToken: "asdhgkfdgh",
            id: "s1556",
            name: "Hariea Test"
          }
        ]}
      />
    );
    const { getByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getByRole("button", { name: /close/i }));

    await waitFor(() => {
      expect(ConnectedAccountsService.clearFbPages).toHaveBeenCalledTimes(1);
      expect(window.location.href).toEqual(url);
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });
  });

  it("shows toast message when a Google account doesn't have a YouTube channel", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["YOUTUBE"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{
          code: YOUTUBE_NO_CHANNEL_ERROR,
          message: "Cannot complete the action because unknown connected account"
        }}
      />
    );

    expect(
      await screen.findByText(/Unable to add YouTube account as no channel is associated with it/i)
    ).toBeInTheDocument();
  });

  it("shows toast message when a connected Instagram account is not connected to a Facebook page", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["INSTAGRAM"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{
          code: INSTA_WARNING_ERROR,
          message: "Cannot complete the action because it's not a professional account"
        }}
      />
    );

    expect(
      await screen.findByText(/To connect your Instagram account you must first link it to your Facebook profile/i)
    ).toBeInTheDocument();
    expect(await screen.findByText(/To do this you can follow/i)).toBeInTheDocument();
    expect(await screen.findByRole("link", { name: /these simple steps/i })).toHaveAttribute(
      "href",
      "https://business.instagram.com/getting-started?fbclid=IwAR2RCQ_lweAva29YXvt7Nfa2wDshHe9oT5LjbHXbGpSKzhIs56G5gfQlrUk"
    );
    expect(
      await screen.findByText(
        /from Instagram’s Help Center, you’ll then be able to connect your Instagram account to the EA Creator Network./i
      )
    ).toBeInTheDocument();
  });

  it("shows toast message when another Instagram account is connected", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["INSTAGRAM"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{
          code: INSTA_CANNOT_CONNECT,
          message: "Cannot complete the action because cannot connect multiple accounts"
        }}
      />
    );

    expect(await screen.findByText(/Unable to connect Instagram account/i)).toBeInTheDocument();
    expect(
      await screen.findByText(/Only one Instagram account can be connected to your Creator Network profile at a time/i)
    ).toBeInTheDocument();
  });

  it("shows toast message when a Twitch account has no write scope", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["TWITCH"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{ code: "no_write_scope", message: "You need to have `write` scope for this resource" }}
      />
    );

    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong");
  });

  it("shows toast message when a facebook account has no required permissions", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{
          code: "invalid-facebook-scope",
          message: "Cannot connect a Facebook Page, not enough authorization scopes were selected"
        }}
      />
    );

    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong");
  });

  it("shows modal to submit content from a connected account", async () => {
    const creator = CreatorWithExpiredAccounts.fromApi(
      aCreatorWithExpiredAccounts({
        connectedAccounts: [aConnectedAccount({ username: "Hariea70a", type: "FACEBOOK", isExpired: false })]
      })
    );
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} creator={creator} isJoined />
    );
    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));
    await waitFor(() => expect(screen.getByRole("button", { name: /Hariea70a/i })).toBeEnabled());

    await userEvent.click(screen.getByRole("button", { name: /Hariea70a/i }));

    const { getByText, getByLabelText, getByRole, getByTestId } = within(await screen.findByRole("dialog"));
    expect(getByText("submit-social-media-content:title")).toHaveClass("modal-header-title");
    expect(getByText("submit-social-media-content:title")).toBeInTheDocument();
    expect(getByText("submit-social-media-content:infoLabel")).toBeInTheDocument();
    expect(getByLabelText("submit-social-media-content:contentUrl")).toBeInTheDocument();
    expect(getByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(getByRole("button", { name: /Submit$/i })).toBeInTheDocument();
    expect(getByTestId("social-media-card-container")).toBeInTheDocument();
  });

  it("closes the submit social content modal  on closing the modal", async () => {
    const creator = CreatorWithExpiredAccounts.fromApi(
      aCreatorWithExpiredAccounts({
        connectedAccounts: [aConnectedAccount({ username: "Hariea70a", type: "FACEBOOK", isExpired: false })]
      })
    );
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["FACEBOOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} creator={creator} isJoined />
    );
    await userEvent.click(await screen.findByRole("button", { name: /submit/i }));
    await waitFor(() => expect(screen.getByRole("button", { name: /Hariea70a/i })).toBeEnabled());
    await userEvent.click(screen.getByRole("button", { name: /Hariea70a/i }));
    const { queryByText, queryByLabelText, queryByRole, getByRole, queryByTestId } = within(
      await screen.findByRole("dialog")
    );

    await userEvent.click(getByRole("button", { name: /close/i }));

    expect(queryByText("submit-social-media-content:title")).not.toBeInTheDocument();
    expect(queryByText("submit-social-media-content:infoLabel")).not.toBeInTheDocument();
    expect(queryByLabelText("submit-social-media-content:contentUrl")).not.toBeInTheDocument();
    expect(queryByRole("button", { name: /cancel/i })).not.toBeInTheDocument();
    expect(queryByRole("button", { name: /submit$/i })).not.toBeInTheDocument();
    expect(queryByTestId("social-media-card-container")).not.toBeInTheDocument();
  });

  it("shows content card with 'Approval not required' status under the single submission deliverable", async () => {
    const deliverableId = "abc123";
    const contentName = "Website content";
    const opportunityName = "Test Opp";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: contentName,
            opportunityName,
            status: null // If status is null, we will show the status as 'Approval Not Required' in contentcard
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "APPROVED", // If the status is 'APPROVED', we will show the status as 'Completed' in DeliverableCard
              id: deliverableId
            })
          ]
        })
      })
    );

    render(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await waitFor(() => {
      expect(screen.getAllByTestId("deliverable-content-card")).toHaveLength(1);
      expect(screen.getByText(contentName)).toBeInTheDocument();
      expect(screen.getByText(opportunityName)).toBeInTheDocument();
      expect(screen.getByText("Completed")).toBeInTheDocument();
      expect(screen.getByText(commonTranslations.contentCard.approvalNotRequired)).toBeInTheDocument();
      expect(screen.queryByText(/Prev/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/Next/i)).not.toBeInTheDocument();
      expect(screen.queryByTestId("second-page-number")).not.toBeInTheDocument();
      expect(screen.queryByTestId("first-page-number")).not.toBeInTheDocument();
    });
  });

  it("shows content card under the unlimited submission deliverable", async () => {
    const deliverableId = "test-unlimited";
    const opportunityName = "Test Opp";
    const status = null; // to show the label "Approval Not Required" in the ContentCard
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "social content 1",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "social content 2",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 1",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 2",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 4",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };

    const deliverablePages = {
      [deliverableId]: [1, 2]
    };
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        currentDeliverablePage: {
          [deliverableId]: 1
        }
      }
    });
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              status: "UNLIMITED_CONTENT",
              id: deliverableId,
              type: "UNLIMITED"
            })
          ]
        })
      })
    );

    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        deliverablePages={deliverablePages}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(screen.getAllByTestId("deliverable-content-card")).toHaveLength(6);
      expect(screen.getAllByText(/^Website content/)).toHaveLength(4);
      expect(screen.getAllByText("Test Opp")).toHaveLength(6);
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.submitUnlimitedContent)
      ).toBeInTheDocument();
      expect(screen.getAllByText(commonTranslations.contentCard.approvalNotRequired)).toHaveLength(6);
      expect(screen.getByText(commonTranslations.buttons.prev)).toBeInTheDocument();
      expect(screen.getByText(commonTranslations.buttons.next)).toBeInTheDocument();
    });
  });

  it("shows second page of content card with the unlimited submission", async () => {
    const deliverableId = "test-unlimited";
    const opportunityName = "Test Opp";
    const status = null; // to show the label "Approval Not Required" in the ContentCard
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "social content 2",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 1",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 2",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            name: "Website content 4",
            opportunityName,
            status
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const deliverablePages = {
      [deliverableId]: [1, 2]
    };
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        currentDeliverablePage: {
          [deliverableId]: 2
        }
      }
    });
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              status: "UNLIMITED_CONTENT",
              id: "test-unlimited",
              type: "UNLIMITED"
            })
          ]
        })
      })
    );
    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        deliverablePages={deliverablePages}
      />
    );

    await userEvent.click(await screen.findByText(commonTranslations.buttons.next));

    await waitFor(() => {
      expect(screen.getByTestId("deliverable-card-container")).toBeInTheDocument();
      expect(screen.getAllByTestId("deliverable-content-card")).toHaveLength(5);
      expect(screen.getAllByText(/^Website content/)).toHaveLength(4);
      expect(screen.getAllByText("Test Opp")).toHaveLength(5);
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.submitUnlimitedContent)
      ).toBeInTheDocument();
      expect(screen.getAllByText(commonTranslations.contentCard.approvalNotRequired)).toHaveLength(5);
      expect(screen.getByText(commonTranslations.buttons.prev)).toBeInTheDocument();
      expect(screen.getByText(commonTranslations.buttons.next)).toBeInTheDocument();
      expect(screen.getByTestId("second-page-number")).toHaveClass("pagination-text-selected");
    });
  });

  it("shows update button for change requested deliverable card", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    expect(await screen.findByRole("button", { name: commonTranslations.buttons.update })).toBeInTheDocument();
  });

  it("shows the feedback for a content", async () => {
    const contentsFeedbackResponse = {
      contentsFeedback: [
        aContentFeedback({
          ...aContentFeedback(),
          formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
        })
      ]
    };
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId
            })
          ]
        })
      })
    );
    (SubmittedContentService.getContentsFeedback as jest.Mock).mockResolvedValue({
      data: contentsFeedbackResponse
    });

    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await userEvent.click(await screen.findByRole("button", { name: /View Changes Required$/ }));

    await waitFor(() => {
      expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
      expect(screen.getByRole("button", { name: /Hide Changes Required$/ })).toBeInTheDocument();
    });
  });

  it("shows the update button for deliverables with changes requested when submission window is closed", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        isSubmissionWindowClosed={true}
      />
    );

    expect(await screen.findByRole("button", { name: commonTranslations.buttons.update })).toBeInTheDocument();
  });

  it("hides the submission window closed badge for approved content when submission window is closed", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "APPROVED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              type: "SINGLE",
              status: "APPROVED",
              id: deliverableId,
              format: "WEBSITE",
              socialAccountTypes: null
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        isSubmissionWindowClosed={true}
      />
    );

    await waitFor(() => {
      expect(
        screen.queryByText(labels.contentSubmissionTabLabels.contentSubmissionLabels.submissionWindowClosed)
      ).not.toBeInTheDocument();
      expect(screen.getByText(labels.contentSubmissionTabLabels.contentSubmissionLabels.completed)).toBeInTheDocument();
    });
  });

  it("shows the website modal on updating the content, when the type is WEBSITE", async () => {
    const contentsFeedbackResponse = {
      contentsFeedback: [
        aContentFeedback({
          ...aContentFeedback(),
          formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
        })
      ]
    };
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId
            })
          ]
        })
      })
    );
    (SubmittedContentService.getContentsFeedback as jest.Mock).mockResolvedValue({
      data: contentsFeedbackResponse
    });

    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );
    await userEvent.click(screen.getByRole("button", { name: /View Changes Required$/ }));

    await userEvent.click(await screen.findByRole("button", { name: commonTranslations.buttons.update }));

    await waitFor(() => {
      expect(screen.getByText(submitWebsiteContentTranslations.title)).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /Submit$/ })).toBeInTheDocument();
    });
  });

  it("shows skeleton loader while feedback is being retrieved", async () => {
    const deliverableId = "test-123";
    const contentsFeedbackResponse = {
      contentsFeedback: [
        aContentFeedback({
          ...aContentFeedback(),
          formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
        })
      ]
    };
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId
            })
          ]
        })
      })
    );

    (SubmittedContentService.getContentsFeedback as jest.Mock).mockImplementation(async () =>
      delay(1000).then(() =>
        Promise.resolve({
          data: contentsFeedbackResponse
        })
      )
    );

    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await userEvent.click(screen.getByRole("button", { name: /View Changes Required$/ }));

    await waitFor(() => {
      expect(screen.getByTestId("deliverable-content-feedback-skeleton-no-animation")).toBeInTheDocument();
    });
  });

  it("hides skeleton loader when error occurred in feedback retrieval", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "CHANGE_REQUESTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              id: deliverableId,
              type: "SINGLE",
              status: "CHANGE_REQUESTED"
            })
          ]
        })
      })
    );

    (SubmittedContentService.getContentsFeedback as jest.Mock).mockImplementation(async () =>
      Promise.reject({
        data: {}
      })
    );

    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await userEvent.click(screen.getByRole("button", { name: /View Changes Required$/ }));

    await waitFor(() => {
      expect(screen.queryByTestId("deliverable-content-feedback-skeleton-no-animation")).not.toBeInTheDocument();
    });
  });

  it("shows warning toast when there's at least one content that requires changes", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "the sims"
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableId,
              socialAccountTypes: null
            })
          ]
        })
      })
    );

    const { unmount } = renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await waitFor(() => {
      const toastContainer = screen.getByRole("alert");
      const { getByRole, getByText } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent("Changes Required");
      expect(getByText(/the sims/i)).toBeInTheDocument();
      expect(getByText(/needs changes/i)).toBeInTheDocument();
    });
    unmount(); //removed the toast
  });

  it("links to each deliverable card that requires changes", async () => {
    const deliverableIdOne = "test-123";
    const deliverableIdTwo = "test-456";
    const content = {
      [deliverableIdOne]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "the sims"
          }) as unknown as ContentsWithDeliverable
        )
      ],
      [deliverableIdTwo]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "fifa"
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableIdOne,
              socialAccountTypes: null
            }),
            aDeliverable({
              format: "WEBSITE",
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableIdTwo,
              socialAccountTypes: null
            })
          ]
        })
      })
    );

    const { container, unmount } = renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined content={content} />
    );

    await waitFor(() => {
      // eslint-disable-next-line testing-library/no-node-access,testing-library/no-container
      expect(container.querySelector(".Toastify__toast.Toastify__toast--warning")).toHaveAttribute(
        "id",
        "SHOW_WARNING_FOR_CHANGES_REQUESTED"
      );
      const toastContainer = screen.getByRole("alert");
      const { getByRole, getByText } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent("Changes Required");
      expect(getByText(/the sims/i)).toBeInstanceOf(HTMLAnchorElement);
      expect(getByText(/the sims/i)).toHaveClass("toast-change-requested");
      expect(getByText(/fifa/i)).toBeInstanceOf(HTMLAnchorElement);
      expect(getByText(/fifa/i)).toHaveClass("toast-change-requested");
    });
    unmount(); //removed the toast
  });

  it("shows toast message when a TikTok account has no required scopes", async () => {
    (ConnectedAccountsService.deleteConnectedAccountErrors as jest.Mock).mockImplementation(() => Promise.resolve());
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["TIKTOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );

    renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        error={{ code: "invalid-tiktok-scope", message: "Invalid Tiktok scope" }}
      />
    );

    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong");
  });

  it("shows TikTok account in the submit dropdown", async () => {
    const creator = CreatorWithExpiredAccounts.fromApi(
      aCreatorWithExpiredAccounts({
        connectedAccounts: [aConnectedAccount({ username: "Hariea70a", type: "TIKTOK", isExpired: false })]
      })
    );
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              socialAccountTypes: ["TIKTOK"],
              format: "SOCIAL"
            })
          ]
        })
      })
    );
    renderWithToast(
      <ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined creator={creator} />
    );

    await userEvent.click(screen.getByRole("button", { name: "Submit" }));

    expect(await screen.findByRole("button", { name: /Hariea70a/i })).toBeInTheDocument();
  });

  it("does not re-open changes required toast after saving content", async () => {
    const url = "https://ea.com";
    const urlScanSuccess = {
      results: [
        {
          url: [url],
          isSecure: true
        }
      ]
    };
    (SubmittedContentService.validateContent as jest.Mock).mockResolvedValue({
      data: urlScanSuccess
    });
    (SubmittedContentService.submitWebsiteContentDeliverable as jest.Mock).mockImplementation(() => Promise.resolve());
    const deliverableIdOne = "test-123";
    const deliverableIdTwo = "test-123";
    const content = {
      [deliverableIdOne]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({
            status: "CHANGE_REQUESTED",
            name: "the sims"
          }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              type: "SINGLE",
              status: "CHANGE_REQUESTED",
              id: deliverableIdOne,
              socialAccountTypes: null
            }),
            aDeliverable({
              format: "WEBSITE",
              type: "SINGLE",
              status: null,
              id: deliverableIdTwo,
              socialAccountTypes: null
            })
          ]
        })
      })
    );
    const analytics = { submittedWebsiteUrl: jest.fn() } as unknown as BrowserAnalytics;
    const { unmount } = renderWithToast(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        analytics={analytics}
      />
    );
    await waitFor(() => {
      const toastContainer = screen.getByRole("alert");
      const { getByRole, getByText } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent("Changes Required");
      expect(getByText(/the sims/i)).toBeInTheDocument();
      expect(getByText(/needs changes/i)).toBeInTheDocument();
    });
    //removing the toast
    await userEvent.click(await screen.findByRole("button", { name: "Close" }));
    await userEvent.click(screen.getByRole("button", { name: /Submit/i }));
    await screen.findByText(/Add content from a Website/i);
    // Fill out the title
    await userEvent.type(screen.getByLabelText("Content Title"), "Title");
    // Fill out the URL
    await userEvent.type(screen.getByLabelText("Please enter the Content URL:"), url);
    // Select the content type
    const dropdown = screen.getByTestId("content-type");
    await userEvent.click(dropdown);
    // Wait for the options to be displayed
    expect(screen.getByRole("list")).toBeInTheDocument();
    await userEvent.click(screen.getByRole("button", { name: "Video" }));

    await userEvent.click(screen.getAllByRole("button", { name: /Submit/i })[1]);

    await waitFor(() => {
      expect(SubmittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.submitWebsiteContentDeliverable).toHaveBeenCalledTimes(1);
      expect(SubmittedContentService.validateContent).toHaveBeenCalledWith({ urls: [url] }, "CREATORS");
      expect(analytics.submittedWebsiteUrl).toHaveBeenCalledTimes(1);
      const toastContainer = screen.getByRole("alert");
      const { getByText, queryByText, getByRole } = within(toastContainer);
      expect(getByRole("heading")).toHaveTextContent(/Content submission successful/i);
      expect(getByText(/You have successfully submitted content for this opportunity!/i)).toBeInTheDocument();
      expect(queryByText(/the sims/i)).not.toBeInTheDocument();
      expect(queryByText(/needs changes/i)).not.toBeInTheDocument();
    });
    unmount();
  });

  it("logs 'Clicked download attachment' event", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasPayments: false,
        hasAttachments: true,
        attachmentsUrl: "http://testUrl.zip"
      })
    );
    const analytics = { clickedDownloadAttachment: jest.fn() } as unknown as BrowserAnalytics;
    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} analytics={analytics} />);
    const downloadAttachmentButton = await screen.findByRole("link", {
      name: contentSubmissionTabLabels.contentSubmissionLabels.downloadAttachments
    });

    await userEvent.click(downloadAttachmentButton);

    await waitFor(() => {
      expect(analytics.clickedDownloadAttachment).toHaveBeenCalledTimes(1);
      expect(analytics.clickedDownloadAttachment).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  it("shows 'Rejected' status label", async () => {
    const deliverableId = "test-123";
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ status: "REJECTED" }) as unknown as ContentsWithDeliverable
        )
      ]
    };
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "WEBSITE",
              socialAccountTypes: null,
              type: "SINGLE",
              status: "REJECTED",
              id: deliverableId
            })
          ]
        })
      })
    );

    render(
      <ContentDeliverablesTab
        {...contentDeliverablesTabProps}
        opportunity={opportunity}
        isJoined
        content={content}
        FLAG_CONTENT_WITH_FINAL_REMARK={false}
      />
    );

    await waitFor(() => {
      expect(screen.queryAllByText(contentSubmissionTabLabels.contentSubmissionLabels.rejected)).toHaveLength(2);
      expect(screen.queryAllByText(contentSubmissionTabLabels.contentSubmissionLabels.notApproved)).toHaveLength(0);
    });
  });

  describe("with FLAG_CONTENT_WITH_FINAL_REMARK enabled", () => {
    it("shows 'Not Approved' status label", async () => {
      const deliverableId = "test-123";
      const content = {
        [deliverableId]: [
          new SubmittedContentWithFinalRemark(
            aSubmittedContentWithFinalRemark({ status: "REJECTED" }) as unknown as ContentsWithReviewFinalRemark
          )
        ]
      };
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          contentSubmission: aContentSubmissionWithDeliverables({
            deliverables: [
              aDeliverable({
                format: "WEBSITE",
                socialAccountTypes: null,
                type: "SINGLE",
                status: "REJECTED",
                id: deliverableId
              })
            ]
          })
        })
      );

      render(
        <ContentDeliverablesTab
          {...contentDeliverablesTabProps}
          opportunity={opportunity}
          isJoined
          content={content}
          FLAG_CONTENT_WITH_FINAL_REMARK={true}
        />
      );

      await waitFor(() => {
        expect(screen.queryAllByText(contentSubmissionTabLabels.contentSubmissionLabels.notApproved)).toHaveLength(2);
        expect(screen.queryAllByText(contentSubmissionTabLabels.contentSubmissionLabels.rejected)).toHaveLength(0);
      });
    });
  });

  it("hide 'Disclosure Policy' and 'Link Submission' sections when file submission enabled for any one of the deliverables", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({
              format: "FILE",
              socialAccountTypes: null
            })
          ]
        })
      })
    );

    render(<ContentDeliverablesTab {...contentDeliverablesTabProps} opportunity={opportunity} isJoined />);

    await waitFor(() => {
      expect(screen.queryByTestId("content-deliverables-disclosure")).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-deliverables-link-submission")).not.toBeInTheDocument();
    });
  });
});
