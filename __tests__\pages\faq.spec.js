import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import React from "react";
import FrequentlyAskedQuestions from "../../pages/faq";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { useDependency } from "../../src/context/DependencyContext";

jest.mock("../../src/context/DependencyContext");

describe("FrequentlyAskedQuestions", () => {
  mockMatchMedia();
  let user = aUser({ status: "ACTIVE" });
  const analytics = { viewedMarketingPage: jest.fn() };
  const frequentlyAskedQuestionProps = {
    user,
    interestedCreator: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({ locale: "en-us", pathname: "/disclosure" }));
    useDependency.mockReturnValue({ analytics, configuration: { SUPPORTED_LOCALES: ["en-us"] } });
  });

  it("shows its title and image", () => {
    const { container } = renderPage(<FrequentlyAskedQuestions {...frequentlyAskedQuestionProps} />);

    expect(screen.getByText(/faqs/i)).toBeInTheDocument();
    expect(container.querySelector(".faq-image")).toBeInTheDocument();
  });

  it("doesn't log 'Viewed Marketing Page' on loading this page when user is not logged in", async () => {
    renderPage(<FrequentlyAskedQuestions {...frequentlyAskedQuestionProps} user={null} />);

    await waitFor(() => expect(analytics.viewedMarketingPage).not.toHaveBeenCalled());
  });

  it("logs 'Viewed Marketing Page' on loading this page", async () => {
    renderPage(<FrequentlyAskedQuestions {...frequentlyAskedQuestionProps} />);

    await waitFor(() => {
      expect(analytics.viewedMarketingPage).toHaveBeenCalledTimes(1);
      expect(analytics.viewedMarketingPage).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/disclosure"
      });
    });
  });
});
