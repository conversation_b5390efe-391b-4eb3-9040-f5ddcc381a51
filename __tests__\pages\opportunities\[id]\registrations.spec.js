import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import Registrations from "../../../../pages/opportunities/[id]/registrations";
import userEvent from "@testing-library/user-event";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import OpportunityService from "../../../../src/api/services/OpportunityService";
import OperationsService from "../../../../src/api/services/OperationsService";
import { mockMatchMedia } from "../../../helpers/window";
import { renderPage } from "../../../helpers/page";
import { useRouter } from "next/router";
import { ampli } from "../../../../analytics/browser/src/ampli";
import { useDetectScreen } from "../../../../utils";
import "next/config";
import { aCreator } from "../../../factories/creators/Creator";
import OpportunityWithDeliverablesResponse from "../../../../src/opportunities/OpportunityWithDeliverables";
import { useDependency } from "../../../../src/context/DependencyContext";
import { errorHandlerFactory } from "../../../../src/errorHandling/errorHandler";

jest.mock("../../../../src/api/services/OpportunityService", () => {
  return {
    ...jest.requireActual("../../../../src/api/services/OpportunityService"),
    getOpportunityWithDeliverables: jest.fn(),
    getOpportunitiesParticipationStatus: jest.fn(),
    getParticipationStatusWithSubmissionInformation: jest.fn()
  };
});
jest.mock("../../../../src/api/services/OperationsService");
jest.mock("../../../../utils", () => ({
  ...jest.requireActual("../../../../utils"),
  useIsMounted: jest.fn().mockImplementation(() => jest.fn().mockReturnValue(true)),
  useDetectScreen: jest.fn().mockImplementation((width) => width === MD_SIZE)
}));
jest.mock("../../../../analytics/browser/src/ampli", () => ({
  ampli: { identify: jest.fn(), receivedErrorMessage: jest.fn() }
}));
jest.mock("next/config");
jest.mock("../../../../src/context/DependencyContext");

const MD_SIZE = 1279;
const LG_SIZE = 10000;
const withGameCodeGuidelineEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Game Code", "game-code", "/opportunities/OPPO123/registrations"],
  ["Content Guidelines", "content-guidelines", "/opportunities/OPPO123/registrations?step=game-code"]
];
const withContentGuidelineEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Content Guidelines", "content-guidelines", "/opportunities/OPPO123/registrations"]
];
const withGameCodeEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Game Code", "game-code", "/opportunities/OPPO123/registrations"]
];

describe("Registrations", () => {
  mockMatchMedia();
  const analytics = {
    completedJoinOpportunityFlow: jest.fn(),
    cancelledJoinOpportunityFlow: jest.fn()
  };
  const router = {
    query: { id: "OPPO123", step: "content-guidelines" },
    pathname: "/opportunities/[id]/registrations",
    locale: "en-us",
    push: jest.fn()
  };
  const registrationsProps = {
    user: { id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" },
    WATERMARKS_URL: "/"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      analytics,
      errorHandler: errorHandlerFactory(false, ["en-us"])
    });
  });

  it("shows a spinner while opportunity information is being retrieved", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123"
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    expect(screen.queryByAltText(/Loading/i)).toBeInTheDocument();
    await waitFor(() => expect(screen.queryByAltText(/Loading/i)).not.toBeInTheDocument());
  });

  it("shows content guidelines by default", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        id: "OPPO123"
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    await waitFor(() => {
      //Breadcrumb title
      expect(screen.getByRole("button", { name: /opportunities:contentGuidelinesTitle/i })).toBeInTheDocument();
      expect(screen.getAllByText(/opportunities:contentGuidelines/i)[0]).toBeInTheDocument();
    });
  });

  it("logs 'Completed Join Opportunity Flow' event after content guidelines", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        id: "OPPO123"
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    OperationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);
    await waitFor(() => expect(screen.queryAllByText(/opportunities:contentGuidelines/i).length).toBeGreaterThan(0));

    await userEvent.click(await screen.findByRole("button", { name: "join" }));

    await waitFor(() => {
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  it("doesn't show content guidelines if opportunity has no content submission guidelines", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        contentSubmission: { guidelines: null },
        hasDeliverables: true
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    await waitFor(() => expect(screen.queryByText(/opportunities:contentGuidelines/i)).not.toBeInTheDocument());
  });

  it("doesn't show download attachments link, if there is no attachments", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        contentSubmission: { guidelines: "<p>I am a content guideline</p>" },
        hasDeliverables: true,
        hasAttachments: false,
        attachmentsUrl: undefined
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    await waitFor(() =>
      expect(screen.queryByRole("link", { name: /content-submission:downloadAttachments$/i })).not.toBeInTheDocument()
    );
  });

  it("shows a link to download attachments", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: "<p>I am a content guideline</p>" },
        hasAttachments: true,
        attachmentsUrl: "https://downloadUrl.zip"
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    expect(await screen.findByRole("link", { name: /content-submission:downloadAttachments$/i })).toHaveAttribute(
      "href",
      "https://downloadUrl.zip"
    );
    expect(await screen.findByRole("link", { name: /content-submission:downloadAttachments$/i })).toHaveAttribute(
      "download"
    );
  });

  it("doesn't show attachments link if opportunity doesn't have content guidelines", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: null },
        hasAttachments: true,
        attachmentsUrl: "https://downloadUrl.zip"
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    await waitFor(() =>
      expect(screen.queryByRole("link", { name: /content-submission:downloadAttachments$/i })).not.toBeInTheDocument()
    );
  });

  xit("shows error toast when error occurred in join opportunity flow", async () => {
    const errorCode = 409;
    const errorMessage = "Cannot join opportunity because: Creator is already a participant of this opportunity.";
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        hasGameCodes: true,
        contentSubmission: { guidelines: null }
      })
    );
    const router = {
      query: { id: opportunity.id, step: "content-guidelines" },
      pathname: "/opportunities/[id]/registrations",
      locale: "en-us",
      push: jest.fn()
    };
    useRouter.mockImplementation(() => router);
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    OperationsService.saveParticipation.mockRejectedValueOnce({
      response: {
        data: {
          code: "join-opportunity-conflicting-action",
          message: errorMessage,
          title: "Conflict",
          type: "https://www.w3.org//Protocols//rfc2616//rfc2616-sec10.html#sec10.4.10",
          status: errorCode
        },
        status: errorCode,
        code: "join-opportunity-conflicting-action",
        message: errorMessage
      }
    });
    const { unmount } = renderPage(<Registrations />);

    await userEvent.click(await screen.findByRole("button", { name: /^join$/i }));

    expect(OpportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
    expect(OpportunityService.getOpportunitiesParticipationStatus).toHaveBeenCalledTimes(1);
    expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(1);
    expect(OperationsService.saveParticipation).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(screen.getByText(/unhandledError/i)).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
    expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    expect(ampli.identify).toHaveBeenCalledTimes(1);
    expect(ampli.receivedErrorMessage).toHaveBeenCalledTimes(1);
    unmount(); // close toast message
  });

  it.each(withGameCodeGuidelineEnabled)(
    "navigates to consent back for all steps enabled, when user taps on 'Back' button in '%s' step for mobile/tablet",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: true
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === MD_SIZE);
      renderPage(<Registrations {...registrationsProps} />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("heading", { level: 6 }));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withContentGuidelineEnabled)(
    "navigates to consent back for only content guidelines enabled, when user taps on 'Back' button in '%s' step for mobile/tablet",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: false
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === MD_SIZE);
      renderPage(<Registrations />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("heading", { level: 6 }));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withGameCodeEnabled)(
    "navigates to consent back for only game codes enabled, when user taps on 'Back' button in '%s' step for mobile/tablet",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: true
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === MD_SIZE);
      renderPage(<Registrations />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("heading", { level: 6 }));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withGameCodeGuidelineEnabled)(
    "navigates to consent back for all steps enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: true
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withContentGuidelineEnabled)(
    "navigates to consent back for only content guidelines enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: false
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withGameCodeEnabled)(
    "navigates to consent back for only game codes enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverablesResponse.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: false,
          hasGameCodes: true
        })
      );
      OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it("redirects to the Join Opportunity page when clicking the close button", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: null }
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    renderPage(<Registrations />);

    await userEvent.click(screen.getByRole("button", { name: /opportunities:exitJoinOpportunityFlow/i }));

    await waitFor(() => {
      expect(analytics.cancelledJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith(`/opportunities/${opportunity.id}`);
    });
  });

  it("shows disclosure policy for paid opportunities", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasPayments: true
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    renderPage(<Registrations {...registrationsProps} />);

    expect(await screen.findByText(/content-submission:paid:contentGuideLineSubTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:paid:contentGuideLineDescription1/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:paid:contentGuideLineDescription2/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:paid:contentGuideLineDescription3/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:paid:contentGuideLineDescription4/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:paid:contentGuideLineDescription5/i)).toBeInTheDocument();
    expect(await screen.findByTestId("content-guide-line-icon-description1")).toBeInTheDocument();
    expect(await screen.findByTestId("content-guide-line-icon-description2")).toBeInTheDocument();
    expect(await screen.findByTestId("content-guide-line-icon-description3")).toBeInTheDocument();
    expect(await screen.findByTestId("content-guide-line-icon-description4")).toBeInTheDocument();
    expect(await screen.findByTestId("content-guide-line-icon-description5")).toBeInTheDocument();
  });

  it("shows disclosure policy for non-paid opportunities", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasPayments: false
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    expect(await screen.findByText(/content-submission:nonPaid:contentGuideLineSubTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:nonPaid:contentGuideLineDescription1/i)).toBeInTheDocument();
    expect(await screen.findByText(/content-submission:nonPaid:contentGuideLineDescription2/i)).toBeInTheDocument();
    expect(screen.queryByText(/content-submission:nonPaid:contentGuideLineDescription3/i)).toBeInTheDocument();
  });
});
