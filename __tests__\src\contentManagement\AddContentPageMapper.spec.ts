import { AddContentPageMapper } from "@src/contentManagement/AddContentPageMapper";

describe("AddContentPageMapper", () => {
  const microCopies = {
    "addContent.accountInformation2": "Account Information 2",
    "addContent.accountInformation1": "Account Information 1",
    "addContent.no": "No",
    "addContent.title": "Title",
    "addContent.urlTitle": "URL Title",
    "addContent.addMoreUrlLabel": "Add More URL",
    "addContent.addNewContent": "Add New Content",
    "addContent.description": "Description",
    "addContent.addContent": "Add Content",
    "addContent.contentSubmissionSucessDescription": "Content Submission Success Description",
    "addContent.reviewContent": "Review Content",
    "addContent.opportunityHeading": "Opportunity Heading",
    "addContent.modalTitle": "Modal Title",
    "addContent.yes": "Yes",
    "addContent.modalDescription": "Modal Description",
    "addContent.addContentInstruction": "Add Content Instruction",
    "addContent.contentSubmissionSucessTitle": "Content Submission Success Title",
    "addContent.connectAnAccount": "Connect an Account",
    "addContent.clickTheIcon": "Click the Icon",
    "addContent.contentInformation3": "Content Information 3",
    "addContent.urlPlaceholder": "URL Placeholder",
    "addContent.contentInformation2": "Content Information 2",
    "addContent.contentInformation1": "Content Information 1"
  };

  it("maps add content page labels", () => {
    const mapper = new AddContentPageMapper();
    const labels = mapper.map(microCopies).addContentPageLabels;

    expect(labels.accountInformation2).toEqual("Account Information 2");
    expect(labels.accountInformation1).toEqual("Account Information 1");
    expect(labels.no).toEqual("No");
    expect(labels.title).toEqual("Title");
    expect(labels.urlTitle).toEqual("URL Title");
    expect(labels.addMoreUrlLabel).toEqual("Add More URL");
    expect(labels.addNewContent).toEqual("Add New Content");
    expect(labels.description).toEqual("Description");
    expect(labels.addContent).toEqual("Add Content");
    expect(labels.contentSubmissionSucessDescription).toEqual("Content Submission Success Description");
    expect(labels.reviewContent).toEqual("Review Content");
    expect(labels.opportunityHeading).toEqual("Opportunity Heading");
    expect(labels.modalTitle).toEqual("Modal Title");
    expect(labels.yes).toEqual("Yes");
    expect(labels.modalDescription).toEqual("Modal Description");
    expect(labels.addContentInstruction).toEqual("Add Content Instruction");
    expect(labels.contentSubmissionSucessTitle).toEqual("Content Submission Success Title");
    expect(labels.connectAnAccount).toEqual("Connect an Account");
    expect(labels.clickTheIcon).toEqual("Click the Icon");
    expect(labels.contentInformation3).toEqual("Content Information 3");
    expect(labels.urlPlaceholder).toEqual("URL Placeholder");
    expect(labels.contentInformation2).toEqual("Content Information 2");
    expect(labels.contentInformation1).toEqual("Content Information 1");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new AddContentPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key addContent.accountInformation2 is absent");
  });
});
