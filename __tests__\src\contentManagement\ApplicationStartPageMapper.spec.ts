import { ApplicationStartPageMapper } from "@src/contentManagement/ApplicationStartPageMapper";

describe("ApplicationStartPageMapper", () => {
  const microCopies = {
    "applicationStart.subTitle": "Sub Title",
    "applicationStart.howPlatformWork": "How Platform Work",
    "applicationStart.appliedDesc": "Applied Description",
    "applicationStart.perks": "Perks",
    "applicationStart.alreadyApplied": "Already Applied",
    "applicationStart.description": "Description",
    "applicationStart.buttons.applyNow": "Apply Now",
    "applicationStart.alreadyAppliedSuffix": "Already Applied Suffix",
    "applicationStart.explore": "Explore",
    "applicationStart.offer": "Offer",
    "applicationStart.title": "Title",
    "applicationStart.appliedDescSuffix": "Applied Description Suffix",
    "applicationStart.descriptionSuffix": "Description Suffix",
    "applicationStart.exploreLeftTitle": "How does the new platform work?",
    "applicationStart.exploreRightTitle": "Opportunities, communities and partnerships"
  };

  it("maps application start page labels", () => {
    const mapper = new ApplicationStartPageMapper();
    const labels = mapper.map(microCopies).applicationStartPageLabels;

    expect(labels.subTitle).toEqual("Sub Title");
    expect(labels.howPlatformWork).toEqual("How Platform Work");
    expect(labels.appliedDesc).toEqual("Applied Description");
    expect(labels.perks).toEqual("Perks");
    expect(labels.alreadyApplied).toEqual("Already Applied");
    expect(labels.description).toEqual("Description");
    expect(labels.buttons.applyNow).toEqual("Apply Now");
    expect(labels.alreadyAppliedSuffix).toEqual("Already Applied Suffix");
    expect(labels.explore).toEqual("Explore");
    expect(labels.offer).toEqual("Offer");
    expect(labels.title).toEqual("Title");
    expect(labels.appliedDescSuffix).toEqual("Applied Description Suffix");
    expect(labels.descriptionSuffix).toEqual("Description Suffix");
    expect(labels.exploreLeftTitle).toEqual("How does the new platform work?");
    expect(labels.exploreRightTitle).toEqual("Opportunities, communities and partnerships");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ApplicationStartPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key applicationStart.subTitle is absent");
  });
});
