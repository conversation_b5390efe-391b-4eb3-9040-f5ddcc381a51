import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import CreatorType from "./../../pages/creator-type";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../helpers/page";
import CreatorsService from "../../src/api/services/CreatorsService";
import { useRouter } from "next/router";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { useAppContext } from "../../src/context";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import { onToastClose } from "../../utils";
import { useDependency } from "../../src/context/DependencyContext";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { CreatorProfile, CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";
import { aCreatorResponse } from "@eait-playerexp-cn/creator-test-fixtures";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("@eait-playerexp-cn/creators-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  onToastClose: jest.fn()
}));
jest.mock("../../src/api/services/CreatorsService");

describe("Creator Type Page", () => {
  let creator;
  const creatorTypeProps = {
    user: aUser({ status: "ACTIVE" })
  };
  const router = { locale: "en-us", pathname: "/creator-type", push: jest.fn() };
  const mockUseDetectScreen = jest.fn();
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/onboarding/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  mockMatchMedia();
  const errorHandler = jest.fn();
  const analytics = {
    canceledOnboardingFlow: jest.fn(),
    confirmedCreatorType: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    creator = aCreatorWithPayableStatus({ creatorTypes: ["YOUTUBER", "LIFESTYLE"] });
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    useDependency.mockReturnValue({
      analytics,
      metadataClient: {},
      errorHandler,
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
    MetadataService.mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue(creatorTypes)
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
  });

  it("save creator Type", async () => {
    renderPage(<CreatorType {...creatorTypeProps} />);

    expect(await screen.findByRole("heading", { name: /creator-type:title/i })).toBeInTheDocument();
    expect(await screen.findByText(/creator-type:infoTitle/i)).toBeInTheDocument();
  });

  it("shows modal with logout option", async () => {
    renderPage(<CreatorType {...creatorTypeProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    renderPage(<CreatorType {...creatorTypeProps} />);
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        page: "/creator-type",
        locale: "en-us"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Creator Type' event when clicking on 'Next' button", async () => {
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CreatorType {...creatorTypeProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(analytics.confirmedCreatorType).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedCreatorType).toHaveBeenCalledWith({
        locale: "en-us",
        creator
      });
      expect(router.push).toHaveBeenCalledWith("/connect-accounts");
    });
  });

  it("shows confirmation modal when user click on 'Back' button with form has unsaved changes", async () => {
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    // There are two cancel buttons in this page used within to find the one in the modal
    const { findByRole, findByText } = within(await screen.findByRole("dialog"));
    expect(await findByRole("heading", { name: /breadcrumb:modalTitle/i })).toBeInTheDocument();
    expect(await findByText(/breadcrumb:modalMessage/i)).toBeInTheDocument();
    expect(await findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /discard/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /save/i })).toBeInTheDocument();
    expect(router.push).not.toBeCalled();
    expect(analytics.confirmedCreatorType).not.toBeCalled();
  });

  it("closes the confirmation modal on discard", async () => {
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /discard/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
    expect(router.push).toHaveBeenCalledWith("/franchises-you-play");
  });

  it("saves the form from confirmation modal and navigates back", async () => {
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/franchises-you-play");
  });

  it("closes the confirmation modal", async () => {
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /Close$/i }));

    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();
  });

  it("navigates to 'franchises-you-play' page when user click on 'Back' button", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<CreatorType {...creatorTypeProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/franchises-you-play"));
  });

  it("detects viewport size and shows page back button for mobile", async () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    renderPage(<CreatorType {...creatorTypeProps} />);

    expect(await screen.findByRole("button", { name: /Back/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /Back/i })).toHaveClass("display-back-bt");
  });

  it("saves the form from confirmation modal and navigates with stepper links", async () => {
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();
    // screen.debug(await screen.findByRole("button"));
    await userEvent.click(await screen.findByRole("button", { name: /Information/i }));
    const { findByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /^save$/i }));

    await waitFor(() => {
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    expect(router.push).toHaveBeenCalledWith("/onboarding/information");
  });

  it("navigates to 'connect-accounts' page when 'Next' is clicked after closing confirmation popup", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    const creatorTypes = [
      aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
      aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
      aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" })
    ];
    creator = aCreatorWithPayableStatus({ creatorTypes });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    renderPage(<CreatorType {...creatorTypeProps} />);
    const creatorType = await screen.findByLabelText(/creator-type:labels.youtuber/i);
    // Select the creator type
    await userEvent.click(creatorType);
    expect(creatorType).toBeChecked();
    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));
    await userEvent.click(await findByRole("button", { name: /cancel/i }));
    expect(queryByRole("heading", { name: /breadcrumb:modalTitle/i })).not.toBeInTheDocument();
    expect(queryByText(/breadcrumb:modalMessage/i)).not.toBeInTheDocument();

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/franchises-you-play"));
  });

  it("navigates to 'Information' page when user click on stepper links without updating any form fields", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        userNavigated: true,
        onboardingSteps: steps
      }
    });
    renderPage(<CreatorType {...creatorTypeProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Information/i }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/onboarding/information"));
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage, userNavigated: true, onboardingSteps: steps }
    });
    const { unmount } = renderWithToast(<CreatorType {...creatorTypeProps} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: "close" }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("show previously selected creator types", async () => {
    const defaultAvatar = "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png";
    const programCode = "creator_network";
    useDependency.mockReturnValue({
      metadataClient: {},
      errorHandler,
      creatorsClient: {},
      configuration: {
        PROGRAM_CODE: programCode,
        FLAG_PER_PROGRAM_PROFILE: true,
        DEFAULT_AVATAR_IMAGE: defaultAvatar
      }
    });
    const creatorProfile = new CreatorProfile(
      aCreatorResponse({ creatorTypes: ["YOUTUBER"] }),
      programCode,
      defaultAvatar
    );
    CreatorService.mockReturnValue({
      getCreator: jest.fn().mockResolvedValue(creatorProfile)
    });

    renderPage(<CreatorType {...creatorTypeProps} />);

    expect(await screen.findByText(/creator-type:labels.youtuber/i)).toBeInTheDocument();
  });
});
