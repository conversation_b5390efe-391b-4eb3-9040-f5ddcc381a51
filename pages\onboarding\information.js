import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import labelsBreadCrumb from "../../config/translations/breadcrumb";
import labelsCommon from "../../config/translations/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import MigrationLayout from "../../components/MigrationLayout";
import Form from "../../components/Form";
import InformationInputs from "../../components/migrations/InformationInputs";
import labelsInformation from "../../config/translations/information";
import labelsAddContent from "../../config/translations/add-content";
import Footer from "../../components/migrations/Footer";
import withAuthenticatedUser from "../../src/utils/WithAuthenticatedUser";
import { useRouter } from "next/router";
import CreatorsService, { CreatorWithPayableStatusProfile } from "../../src/api/services/CreatorsService";
import { useAppContext } from "../../src/context";
import Error from "../_error";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  onToastClose,
  SESSION_USER,
  toastContent,
  useAsync,
  VALIDATION_ERROR
} from "../../utils";
import withUnregisteredUser from "../../src/utils/WithUnregisteredUser";
import Loading from "../../components/Loading";
import { Toast, useToast } from "../../components/toast";
import { AuthenticatedUserFactory } from "../../src/analytics/BrowserAnalytics";
import RedirectException from "../../src/utils/RedirectException";
import labelsOpportunities from "../../config/translations/opportunities";
import CancelRegistrationModal from "../../components/pages/interested-creators/CancelRegistrationModal";
import labelsProfile from "../../config/translations/profile";
import flags from "../../utils/feature-flags";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import runtimeConfiguration from "../../src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import informationProps from "../../src/serverprops/InformationProps";
import verifyIncompleteRegistration from "../../src/serverprops/middleware/VerifyIncompleteRegistration";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export default function Information({
  user,
  futureCreator,
  registrationCode,
  FLAG_COUNTRIES_BY_TYPE,
  FLAG_ONBOARDING_CUSTOM_LINKS
}) {
  const {
    analytics,
    metadataClient,
    errorHandler,
    creatorsClient,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(
    () => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE),
    [creatorsClient, DEFAULT_AVATAR_IMAGE]
  );
  const {
    dispatch,
    state: { exceptionCode = null, sessionUser = null, onboardingSteps, isValidationError, isError } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [creator, setCreator] = useState(null);
  const [platforms, setPlatform] = useState(null);
  const [countries, setCountries] = useState(null);
  const { t } = useTranslation(["common", "breadcrumb", "information", "opportunities", "add-content"]);
  const { error: errorToast } = useToast();
  const { addMoreUrlLabel } = labelsAddContent(t);
  const { layout, labels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      labels: {
        ...labelsInformation(t),
        header: {
          calendar: labelsCommon(t).header.calendar
        },
        profileLabels: { updateAvatar: labelsProfile(t).updateAvatar },
        buttons: { ...labelsCommon(t).buttons }
      }
    };
  }, [t]);
  const {
    main: { unhandledError },
    buttons: { remove }
  } = layout;
  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = labels;
  const onBoardingFormLabels = Object.assign(
    {},
    {
      ...labels,
      addMoreUrlLabel,
      remove
    }
  );
  const onClose = useCallback(() => {
    setShowConfirmation(true);
  }, []);
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);

  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  const defaultValues = useMemo(() => {
    const contentUrls = [{ url: "", followers: "" }];
    return { contentUrls };
  }, []);
  // Save form data and navigate to next page
  const submitHandle = useCallback(
    async (data) => {
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      data.dateOfBirth = LocalizedDate.fromFormattedDate(data.dateOfBirth).format("YYYY-MM-DD");
      data.nucleusId = creator.accountInformation.nucleusId;
      data.originEmail = creator.accountInformation.originEmail;
      try {
        // Creators BFF POST and PUT
        const primaryPlatform = { id: data.primaryPlatform.value, type: "PRIMARY" };
        const primaryPlatformValues = data.secondaryPlatforms.map(
          (secondaryPlatform) =>
            secondaryPlatform.value && {
              id: secondaryPlatform.value,
              type: "SECONDARY"
            }
        );
        primaryPlatformValues.push(primaryPlatform);
        const accountInformation = {
          defaultGamerTag: user.username,
          ...(!creator.id && { nucleusId: data.nucleusId }),
          firstName: data.firstName,
          lastName: data.lastName,
          ...(!creator.id && { preferredName: data.firstName }),
          ...(!creator.id && { preferredPronouns: "" }),
          originEmail: data.originEmail,
          dateOfBirth: data.dateOfBirth
        };
        const bodyValues = {
          accountInformation: accountInformation,
          preferredPlatforms: primaryPlatformValues,
          mailingAddress: {
            country: {
              code: data.country.value,
              name: data.country.label
            },
            street: data.street,
            state: data.state,
            city: data.city,
            zipCode: data.zipCode
          },
          program: PROGRAM_CODE,
          ...(registrationCode && { registrationCode: registrationCode })
        };
        FLAG_PER_PROGRAM_PROFILE
          ? creator.id
            ? await creatorService.updateCreator({
                ...bodyValues,
                program: { code: PROGRAM_CODE }
              })
            : await creatorService.registerCreator(bodyValues)
          : await CreatorsService.register({ information: data });

        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        analytics.confirmedPlatform({
          locale: router.locale,
          primaryPlatform: data.primaryPlatform.label,
          secondaryPlatforms:
            data.secondaryPlatforms?.length > 0 ? data.secondaryPlatforms.map((platform) => platform.label) : []
        });
        router.push("/franchises-you-play");
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, creator, router, onboardingSteps]
  );
  const { pending, execute: submitInformation } = useAsync(submitHandle, false);
  useEffect(() => {
    if (!futureCreator) {
      (async () => {
        // Creators BFF GET
        try {
          const creator = FLAG_PER_PROGRAM_PROFILE
            ? await creatorService.getCreator(PROGRAM_CODE)
            : (await CreatorsService.getCreatorWithPayableStatus()).data;
          if (FLAG_PER_PROGRAM_PROFILE) {
            if (creator.preferredPrimaryPlatform) {
              creator.preferredPrimaryPlatform = {
                value: creator.preferredPrimaryPlatform.id,
                label: creator.preferredPrimaryPlatform.name
              };
            }
            creator.preferredSecondaryPlatforms = creator.preferredSecondaryPlatforms.map((platform) => {
              return {
                ...platform,
                value: platform.id,
                label: platform.name
              };
            });
          }
          setCreator(creator);
        } catch (e) {
          errorHandler(stableDispatch, e);
        }
      })();
    } else {
      setCreator(
        new CreatorWithPayableStatusProfile({
          accountInformation: {
            originEmail: futureCreator.email,
            dateOfBirth: futureCreator.dateOfBirth,
            nucleusId: futureCreator.nucleusId,
            defaultGamerTag: user.username
          }
        })
      );
    }
    const countries = FLAG_COUNTRIES_BY_TYPE ? metadataService.getCountriesMatching() : metadataService.getCountries();
    countries.then((res) => setCountries(res)).catch((e) => errorHandler(stableDispatch, e));
    metadataService
      .getPlatformsMatching({ type: "SITE" })
      .then((platforms) => setPlatform(platforms))
      .then(() => {
        analytics.startedOnboardingFlow({ locale: router.locale });
      })
      .catch((e) => errorHandler(stableDispatch, e));
  }, [user, stableDispatch, futureCreator]);

  useEffect(() => user && stableDispatch({ type: SESSION_USER, data: user }), [user, stableDispatch]);
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);
  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <MigrationLayout
      pageTitle={labels.title}
      {...{
        ...layout,
        onClose,
        isRegistrationFlow: true,
        isOnboardingFlow: true,
        stableDispatch,
        completed: layout.completed
      }}
      labels={{
        back: layout.buttons.back,
        title: layout.header.creatorNetwork,
        close: layout.buttons.closeHeader
      }}
    >
      <div className="mg-intro">
        <h3 className="mg-intro-title">{labels.title}</h3>
        <div className="mg-intro-description">
          {t("information:description", { ...{ displayName: creator?.accountInformation?.defaultGamerTag } })}
        </div>
      </div>
      {creator && countries && platforms && (
        <Form mode="onChange" onSubmit={submitInformation} {...(FLAG_ONBOARDING_CUSTOM_LINKS && { defaultValues })}>
          <InformationInputs
            {...{
              labels: { ...onBoardingFormLabels },
              user,
              futureCreator,
              creator,
              countries,
              platforms,
              stableDispatch,
              FLAG_ONBOARDING_CUSTOM_LINKS
            }}
          />
          <Footer
            {...{
              buttons: layout.buttons,
              onCancel: onClose,
              disableSubmit: pending,
              isPending: pending
            }}
          />
        </Form>
      )}
      {!creator && (
        <div className="loader">
          <Loading />
        </div>
      )}
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyIncompleteRegistration)
      .use(addLocaleCookie(locale))
      .get(informationProps(locale));

    return await router.run(req, res);
  }
  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const futureCreator = req.session.futureCreator || null;
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;
  const registrationCode = req.session.registrationCode || null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      futureCreator,
      registrationCode,
      ...(await serverSideTranslations(locale, [
        "common",
        "breadcrumb",
        "information",
        "opportunities",
        "add-content"
      ])),
      showInitialMessage: req.session.showInitialMessage || false,
      FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled(),
      FLAG_ONBOARDING_CUSTOM_LINKS: flags.isOnboardingCustomLinksEnabled()
    }
  };
};
