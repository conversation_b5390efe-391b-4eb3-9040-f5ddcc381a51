import { ApplicationCompletePageMapper } from "@src/contentManagement/ApplicationCompletePageMapper";

describe("ApplicationCompletePageMapper", () => {
  const microCopies = {
    "applicationComplete.title": "Application Complete Title",
    "applicationComplete.backHome": "Back Home",
    "applicationComplete.description": "Description",
    "applicationComplete.submissionCompleteDescription": "Submission Complete Description",
    "applicationComplete.submissionDate": "Submission Date",
    "applicationComplete.email": "Email",
    "applicationComplete.subTitle": "Sub Title",
    "applicationComplete.unReviewed": "Unreviewed",
    "applicationComplete.submissionCompleteSubTitle": "Submission Complete Sub Title",
    "applicationComplete.gamerTag": "Gamer Tag",
    "applicationComplete.status": "Status",
    "applicationComplete.programLabel": "Program",
    "applicationComplete.programName": "Support a Creator"
  };

  it("maps application complete page labels", () => {
    const mapper = new ApplicationCompletePageMapper();
    const labels = mapper.map(microCopies).applicationCompletePageLabels;

    expect(labels.title).toEqual("Application Complete Title");
    expect(labels.backHome).toEqual("Back Home");
    expect(labels.description).toEqual("Description");
    expect(labels.submissionCompleteDescription).toEqual("Submission Complete Description");
    expect(labels.submissionDate).toEqual("Submission Date");
    expect(labels.email).toEqual("Email");
    expect(labels.subTitle).toEqual("Sub Title");
    expect(labels.unReviewed).toEqual("Unreviewed");
    expect(labels.submissionCompleteSubTitle).toEqual("Submission Complete Sub Title");
    expect(labels.gamerTag).toEqual("Gamer Tag");
    expect(labels.status).toEqual("Status");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ApplicationCompletePageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key applicationComplete.title is absent");
  });
});
