import { ApplicationPendingPageMapper } from "@src/contentManagement/ApplicationPendingPageMapper";

describe("ApplicationPendingPageMapper", () => {
  const microCopies = {
    "applicationPending.returnToCreatorNetwork": "Return to Creator Network",
    "applicationPending.gamerTag": "Gamer Tag",
    "applicationPending.pending": "Pending",
    "applicationPending.submissionUpdateDescription": "Submission Update Description",
    "applicationPending.submissionUpdate": "Submission Update",
    "applicationPending.title": "Title",
    "applicationPending.description": "Description",
    "applicationPending.reviewAndResubmit": "Review and Resubmit",
    "applicationPending.submissionReceived": "Submission Received",
    "applicationPending.submissionReceivedDescription": "Submission Received Description",
    "applicationPending.unReviewed": "Unreviewed",
    "applicationPending.status": "Status",
    "applicationPending.email": "Email",
    "applicationPending.submissionDate": "Submission Date",
    "applicationPending.backHome": "Return to Home",
    "applicationPending.subTitle": "Thank you for submitting your request to join the EA Support a Creator Program.",
    "applicationRejected.programLabel": "Program",
    "applicationRejected.programName": "Support a Creator",
    "applicationPending.applicationPendingDescription":
      "Your submission has been sent for review by a member of the EA Support a Creator Team."
  };

  it("maps application pending page labels", () => {
    const mapper = new ApplicationPendingPageMapper();
    const labels = mapper.map(microCopies).applicationPendingLabels;

    expect(labels.returnToCreatorNetwork).toEqual("Return to Creator Network");
    expect(labels.gamerTag).toEqual("Gamer Tag");
    expect(labels.pending).toEqual("Pending");
    expect(labels.submissionUpdateDescription).toEqual("Submission Update Description");
    expect(labels.submissionUpdate).toEqual("Submission Update");
    expect(labels.title).toEqual("Title");
    expect(labels.description).toEqual("Description");
    expect(labels.reviewAndResubmit).toEqual("Review and Resubmit");
    expect(labels.submissionReceived).toEqual("Submission Received");
    expect(labels.submissionReceivedDescription).toEqual("Submission Received Description");
    expect(labels.unReviewed).toEqual("Unreviewed");
    expect(labels.status).toEqual("Status");
    expect(labels.email).toEqual("Email");
    expect(labels.submissionDate).toEqual("Submission Date");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ApplicationPendingPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key applicationPending.returnToCreatorNetwork is absent");
  });
});
