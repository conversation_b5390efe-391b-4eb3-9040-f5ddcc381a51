import { ApplicationAcceptedPageMapper } from "@src/contentManagement/ApplicationAcceptedPageMapper";

describe("ApplicationAcceptedPageMapper", () => {
  const microCopies = {
    "applicationAccepted.descriptionPara3": "Description Paragraph 3",
    "applicationAccepted.descriptionPara1": "Description Paragraph 1",
    "applicationAccepted.returnToCreatorNetwork": "Return to Creator Network",
    "applicationAccepted.descriptionPara2": "Description Paragraph 2",
    "applicationAccepted.pageTitle": "Page Title",
    "applicationAccepted.title": "Title",
    "applicationAccepted.completeYourProfile": "Complete Your Profile"
  };

  it("maps application accepted page labels", () => {
    const mapper = new ApplicationAcceptedPageMapper();
    const labels = mapper.map(microCopies).applicationAcceptedLabels;

    expect(labels.descriptionPara3).toEqual("Description Paragraph 3");
    expect(labels.descriptionPara1).toEqual("Description Paragraph 1");
    expect(labels.returnToCreatorNetwork).toEqual("Return to Creator Network");
    expect(labels.descriptionPara2).toEqual("Description Paragraph 2");
    expect(labels.pageTitle).toEqual("Page Title");
    expect(labels.title).toEqual("Title");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ApplicationAcceptedPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key applicationAccepted.descriptionPara3 is absent");
  });
});
