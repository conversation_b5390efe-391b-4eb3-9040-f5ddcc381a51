import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsProfile from "../config/translations/profile";
import labelsBreadCrumb from "../config/translations/breadcrumb";
import labelsConnectAccounts from "../config/translations/connect-accounts";
import MigrationLayout from "../components/MigrationLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Form from "../components/Form";
import Footer from "../components/migrations/Footer";
import ConnectAccountsInputs from "../components/migrations/ConnectAccountsInputs";
import ConnectedAccount from "../components/migrations/ConnectedAccounts";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import withUnregisteredUser from "../src/utils/WithUnregisteredUser";
import {
  COMPLETED_ONBOARDING_STEPS,
  DOMAIN_ERROR,
  ERROR,
  getExtractedErrorMessage,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  useIsMounted,
  VALIDATION_ERROR
} from "../utils";
import ConditionalWrapper from "../components/ConditionalWrapper";
import { useRouter } from "next/router";
import { Toast, useToast } from "../components/toast";
import FormTitle from "../components/formTitle/FormTitle";
import Loading from "../components/Loading";
import Error from "./_error";
import CreatorsService from "../src/api/services/CreatorsService";
import ConnectedAccountsService from "../src/api/services/ConnectedAccountsService";
import RedirectException from "../src/utils/RedirectException";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import labelsOpportunities from "../config/translations/opportunities";
import CancelRegistrationModal from "../components/pages/interested-creators/CancelRegistrationModal";
import ConnectFacebookPagesModal from "../components/pages/ConnectFacebookPagesModal";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { useDependency } from "@src/context/DependencyContext";
import featureFlags from "utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import connectAccountsProps from "../src/serverprops/ConnectAccountsProps";
import verifyIncompleteRegistration from "../src/serverprops/middleware/VerifyIncompleteRegistration";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const REMOVE_ONLY_ACCOUNT = "disconnect-account-conflicting-action";
const YOUTUBE_NO_CHANNEL_ERROR = "save-you-tube-account-unknown-connected-account";
const REMOVE_ONLY_ACCOUNT_INVALID = "disconnect-account-invalid-input";
const INSTAGRAM_STEPS_LINK =
  "https://business.instagram.com/getting-started?fbclid=IwAR2RCQ_lweAva29YXvt7Nfa2wDshHe9oT5LjbHXbGpSKzhIs56G5gfQlrUk";
const INSTA_WARNING_ERROR = "save-instagram-account-unknown-instagram-business-account";
const INSTA_CANNOT_CONNECT = "save-instagram-account-cannot-connect-account";

const ConnectAccountsHeader = memo(function ConnectAccountsHeader({ labels, myProfileView }) {
  return (
    <div className="mg-connect-accounts">
      {myProfileView && <FormTitle title={labels.connectedAccounts} />}
      <h4 className="mg-connect-accounts-title">{labels.title}</h4>
      <div className="mg-connect-accounts-description">{labels.message}</div>
    </div>
  );
});

export const ConnectAccountsForm = memo(function ConnectAccountsForm({
  layout,
  labels,
  setAccountToRemove,
  setShowAddConfirmation,
  accountToRemove,
  showAddConfirmation,
  onClose,
  accounts,
  myProfileView,
  handleSubmit,
  isLoading,
  isPending,
  setShowRemoveAccountModal,
  showRemoveAccountModal,
  isInterestedCreator
}) {
  return myProfileView ? (
    <div className="connect-accounts-form">
      <div className="connect-accounts">
        <ConnectedAccount
          {...{
            layout,
            labels,
            setAccountToRemove,
            accountToRemove,
            accounts,
            isLoading,
            setShowAddConfirmation,
            showAddConfirmation,
            setShowRemoveAccountModal,
            showRemoveAccountModal,
            isInterestedCreator
          }}
        />
        <ConnectAccountsInputs
          {...{
            labels,
            setShowAddConfirmation,
            showAddConfirmation,
            isInterestedCreator
          }}
        />
      </div>
    </div>
  ) : (
    <>
      <Form mode="onChange" onSubmit={handleSubmit}>
        <div className="connect-accounts-form">
          <div className="connect-accounts">
            <ConnectedAccount
              {...{
                layout,
                labels,
                setAccountToRemove,
                accountToRemove,
                accounts,
                isLoading,
                setShowAddConfirmation,
                showAddConfirmation,
                setShowRemoveAccountModal,
                showRemoveAccountModal
              }}
            />
            <ConnectAccountsInputs {...{ labels, setShowAddConfirmation, showAddConfirmation }} />
            <Footer
              {...{
                buttons: layout.buttons,
                onCancel: onClose,
                disableSubmit: !accounts.length || isPending,
                isPending
              }}
            />
          </div>
        </div>
      </Form>
    </>
  );
});

export default memo(function ConnectAccounts({ error = null, pages = [], user, myProfileView, invalidTikTokScope }) {
  const {
    analytics,
    errorHandler,
    creatorsClient,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const { id: creatorId } = user || {};
  const {
    dispatch,
    state: {
      exceptionCode = null,
      sessionUser = null,
      popupOpened = false,
      isLoading,
      isError,
      isValidationError,
      domainError,
      onboardingSteps
    } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast, warning } = useToast();

  //TODO: To manipulate the creator data
  const [creator, setCreator] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const [accountToRemove, setAccountToRemove] = useState(null);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);
  const [showPagesModal, setShowPagesModal] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  const [hasAccountConnectedRun, setHasAccountConnectedRun] = useState(false);
  const { t } = useTranslation(["common", "breadcrumb", "connect-accounts", "opportunities"]);
  const isMounted = useIsMounted();
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const { layout, labels } = useMemo(() => {
    return {
      layout: {
        ...labelsCommon(t),
        ...labelsBreadCrumb(t),
        ...labelsOpportunities(t)
      },
      labels: { ...labelsConnectAccounts(t), ...labelsProfile(t) }
    };
  }, [t]);
  const {
    confirmationDesc1,
    confirmationDesc2,
    modalConfirmationTitle,
    modalConfirmationTitleFB,
    messages: { actionTitle, actionDescription1, actionDescription2, actionDescription3, actionDescription4 }
  } = labels;
  const {
    removeAccountDescription,
    youtubeNoChannelError,
    cannotConnectInstaAccount,
    cannotConnectInstaAccountHeader
  } = labels.messages;
  const {
    main: { unhandledError }
  } = layout;
  const accounts = FLAG_PER_PROGRAM_PROFILE
    ? creator?.connectedAccounts
      ? creator?.connectedAccounts?.filter((account) => !account.disconnected) || []
      : []
    : creator?.accounts || [];
  const instaWarningContent = useMemo(() => {
    return (
      <div className="connect-account-insta-warning">
        <p>{actionDescription1}</p>
        <br />
        <p key="actionDescription2">
          {actionDescription2}{" "}
          <a className="connect-account-insta-steps" target="_blank" rel="noreferrer" href={INSTAGRAM_STEPS_LINK}>
            {actionDescription3}
          </a>
          {actionDescription4}
        </p>
      </div>
    );
  }, [actionDescription1, actionDescription2, actionDescription3, actionDescription4]);
  const errorMap = useMemo(
    () =>
      new Map([
        [INSTA_CANNOT_CONNECT, cannotConnectInstaAccount],
        [REMOVE_ONLY_ACCOUNT, removeAccountDescription],
        [REMOVE_ONLY_ACCOUNT_INVALID, removeAccountDescription],
        [YOUTUBE_NO_CHANNEL_ERROR, youtubeNoChannelError]
      ]),
    [removeAccountDescription, youtubeNoChannelError, cannotConnectInstaAccount]
  );

  const toggleFacebookPagesModal = () => {
    if (pages.length) {
      setShowPagesModal(true);
    } else {
      setShowPagesModal(false);
    }
  };

  //----------------------------------
  // Fetch Channels on Removal.
  // ReFetch Creators on window closed
  //----------------------------------
  async function fetchData() {
    try {
      // very imp condition for performance
      stableDispatch({ type: LOADING, data: true });
      // Creators BFF GET
      const creator = FLAG_PER_PROGRAM_PROFILE
        ? await creatorService.getCreator(PROGRAM_CODE)
        : (await CreatorsService.getCreatorWithPayableStatus()).data;
      setCreator(creator);
      toggleFacebookPagesModal();
      stableDispatch({ type: LOADING, data: false });
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
  }

  useEffect(() => {
    if (!accountToRemove) {
      fetchData();
    }
  }, [accountToRemove]);

  useEffect(() => {
    if (invalidTikTokScope) {
      stableDispatch({ type: ERROR, data: layout.main.unhandledError });
    }
  }, [invalidTikTokScope]);

  useEffect(() => {
    fetchData();
  }, []);

  //----------------------------------
  // ReFetch Creators on window closed
  //----------------------------------
  useEffect(() => {
    if (!popupOpened && showAddConfirmation) {
      if (isMounted()) {
        setShowAddConfirmation(false);
      }
    }
  }, [popupOpened, showAddConfirmation, isMounted]);

  //-----------------------------
  // Error Handling
  //-----------------------------
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch, unhandledError)
        }
      );
    }
  }, [isError, isValidationError, errorToast, stableDispatch, unhandledError]);

  useEffect(() => {
    if (domainError) {
      const errorMessage = getExtractedErrorMessage(errorMap, domainError, unhandledError);
      errorToast(<Toast header={unhandledError} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />, {
        onClose: () => onToastClose(DOMAIN_ERROR, stableDispatch, unhandledError)
      });
    }
  }, [domainError, unhandledError, stableDispatch, errorMap]);

  useEffect(() => {
    if (!hasAccountConnectedRun) {
      setHasAccountConnectedRun(true);
      if (error?.code === INSTA_WARNING_ERROR) {
        warning(
          <Toast header={actionTitle} content={instaWarningContent} closeButtonAriaLabel={layout.buttons.close} />
        );
      } else if (error && errorMap) {
        const header = error?.code === INSTA_CANNOT_CONNECT ? cannotConnectInstaAccountHeader : unhandledError;
        const errorMessage = getExtractedErrorMessage(errorMap, error, unhandledError);
        errorToast(<Toast header={header} content={errorMessage} closeButtonAriaLabel={layout.buttons.close} />);
      }
    }
  }, [error, actionTitle, instaWarningContent, warning, stableDispatch, errorToast, unhandledError, errorMap, cannotConnectInstaAccountHeader, hasAccountConnectedRun]);

  useEffect(() => {
    return () => {
      // clear context states on unmount
      stableDispatch({ type: ERROR, data: false });
      stableDispatch({ type: VALIDATION_ERROR, data: false });
      stableDispatch({ type: DOMAIN_ERROR, data: false });
    };
  }, [stableDispatch]);

  //-----------------------------
  // Handlers or callbacks
  //-----------------------------
  const onChange = ({ target: { id: pageId, value: pageAccessToken } }) => setSelectedPage({ pageId, pageAccessToken });

  const onClose = useCallback(() => setShowConfirmation(!showConfirmation), [showConfirmation]);
  const onCloseFb = useCallback(async () => {
    try {
      setShowPagesModal(false);
      // Unset FBPages from session.
      await ConnectedAccountsService.clearFbPages();
    } catch (e) {
      errorHandler(stableDispatch, e);
    }
    window.location.reload(); // refresh to trigger getServersideProps rerun and spew new session props
  }, [stableDispatch]);

  const onGoBack = () => router.push("/creator-type");

  const wrapper = useCallback(
    (children) => (
      <MigrationLayout
        pageTitle={labels.title}
        {...{
          ...layout,
          onClose,
          isRegistrationFlow: true,
          isOnboardingFlow: true,
          stableDispatch,
          onGoBack,
          completed: layout.completed
        }}
        labels={{
          back: layout.buttons.back,
          title: layout.header.creatorNetwork,
          close: layout.buttons.closeHeader
        }}
      >
        {children}
      </MigrationLayout>
    ),
    [layout, onClose, labels.title]
  );

  const onConnectFbC = useCallback(async () => {
    if (selectedPage) {
      try {
        await ConnectedAccountsService.connectFbPages(selectedPage);
        setSelectedPage(null);
      } catch (error) {
        errorHandler(stableDispatch, error);
      }
      await ConnectedAccountsService.clearAccountType();
      window.location.reload(); // refresh to trigger getServersideProps rerun and spew new session props
    }
  }, [selectedPage, stableDispatch]);

  const router = useRouter();
  const handleSubmit = useCallback(() => {
    const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
    analytics.confirmedSocialMediaChannel({ locale: router.locale, creator });
    router.push("/communication-preferences");
    stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
    return Promise.resolve(true);
  }, [router, creator, onboardingSteps]);

  const handleCancelRegistration = useCallback(() => {
    analytics.canceledOnboardingFlow({ locale: router.locale, page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  const { pending, execute: onConnectFb } = useAsync(onConnectFbC, false);
  const { pending: isPending, execute: handleSubmitClb } = useAsync(handleSubmit, false);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  const connectFaceBookModalLabels = {
    title: modalConfirmationTitleFB,
    cancel: layout.buttons.cancel,
    connect: layout.buttons.connect,
    close: layout.buttons.close
  };

  return (
    <ConditionalWrapper {...{ condition: !myProfileView, wrapper }}>
      <div className={`connect-accounts-container ${myProfileView && "myprofile-view"}`}>
        <ConnectAccountsHeader {...{ labels, myProfileView }} />
        {isLoading && (
          <div className="loader">
            <Loading />
          </div>
        )}
        <ConnectAccountsForm
          {...{
            layout,
            labels,
            setShowAddConfirmation,
            accountToRemove,
            showAddConfirmation,
            setAccountToRemove,
            onClose,
            accounts,
            myProfileView,
            handleSubmit: handleSubmitClb,
            isLoading,
            isPending,
            showRemoveAccountModal,
            setShowRemoveAccountModal
          }}
        />

        {showConfirmation && (
          <CancelRegistrationModal
            {...{
              labels: cancelRegistrationModalLabels,
              handleModalClose: onClose,
              handleCancelRegistration
            }}
          />
        )}

        {showPagesModal && (
          <ConnectFacebookPagesModal
            {...{
              labels: connectFaceBookModalLabels,
              onClose: onCloseFb,
              onChange,
              pages,
              onConnect: onConnectFb,
              pending,
              selectedPage
            }}
          />
        )}
      </div>
    </ConditionalWrapper>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyIncompleteRegistration)
      .use(addLocaleCookie(locale))
      .get(connectAccountsProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withUnregisteredUser(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;
  const { pages = [] } = req.session.fbPages || {};
  const error = req.session.error || null;
  const invalidTikTokScope = req.session.INVALID_TIKTOK_SCOPE || null;
  delete req.session.error;
  delete req.session.INVALID_TIKTOK_SCOPE;
  req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      error,
      pages,
      ...(await serverSideTranslations(locale, ["common", "breadcrumb", "connect-accounts", "opportunities"])),
      invalidTikTokScope
    }
  };
};
