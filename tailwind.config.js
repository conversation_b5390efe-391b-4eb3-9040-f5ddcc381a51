const coreUiKit = require("@eait-playerexp-cn/core-ui-kit/config");

module.exports = coreUiKit({
  content: ["pages/**/*.{ts,js,jsx,tsx}", "components/**/*.{ts,js,jsx,tsx}"],
  theme: {
    extend: {
      rotate: {
        "-7.16": "-7.16"
      },
      backgroundImage: {
        "hero-3D-Circle-2": "url('/img/3D-Circle-2.png')",
        "hero-3D-Circle-2-lg": "url('/img/3D-Circle-2.png')",
        "hero-3D-Circle-1": "url('/img/3D-Circle-1.png')",
        "hero-3D-Circle-1-md": "url('/img/3D-Circle-1.png')",
        "hero-bg-sm": "url(/img/Hero-3D-square-sm.png)",
        "hero-bg-lg": "url(/img/3D-square-and-triangle.png)",
        "hero-image-reward": "var(--rewards-hero-background-square)",
        "hero-image-reward-web": "var(--rewards-hero-background-square)",
        "reward-competition-left-avatar": "var(--rewards-left-avatar)",
        "start-page-web": "linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "start-page-tablet":
          "linear-gradient(186.57deg, rgba(255, 71, 71, 0.77) -3.69%, rgba(12, 15, 64, 0.77) 12.16%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "start-page-mobile":
          "linear-gradient(188.41deg, rgba(255, 71, 71, 0.77) -7.25%, rgba(12, 15, 64, 0.77) 43.94%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-web": "linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-default":
          "linear-gradient(186.57deg, rgba(255, 71, 71, 0.77) -3.69%, rgba(12, 15, 64, 0.77) 12.16%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-shape": "var(--migration-background)",
        "reward-eclipse-5": "url('/img/rewards/bg-exclusive-competition.png')",
        "reward-image-carousal": "var(--rewards-carousel)",
        "reward-competition-right-avatar": "var(--rewards-right-avatar)",
        "reward-exclusive-comp-web": "url(/img/3D-Sqaure-rewards.png)",
        "reward-exclusive-comp-tab": "url(/img/3D-Sqaure-rewards.png)",
        "reward-competition-games": "var(--rewards-competition)",
        "hiws-character-1": "url(/img/hiws/Character-1.svg)",
        "signup-complete": "linear-gradient(23.91deg, #0D1042 69.51%, #FF4747 149.49%)",
        "interested-creator-layout-default": "linear-gradient(23.91deg, #0D1042 69.51%, #FF4747 149.49%)",
        "interested-creator-layout": "var(--interested-creator-layout-background)",
        "interested-creator-bg-shape": "var(--interested-creator-shape-background)",
        "down-arrow-icon-background": "var(--down-arrow-icon-background)"
      },
      backgroundPosition: {
        "hero-bg-lg": "top 0px right 0px",
        "hero-bg-sm": "top -8px right -150px",
        "hiws-left-img-lg": "left 0px top 0px",
        "hiws-hero-sm": "top 0px right 0px",
        "hero-image-reward": "right -170px top -50px",
        "hero-image-reward-web": "right -170px top 25px",
        "hero-3D-Circle-1": "right 28px top -75px",
        "hero-3D-Circle-1-md": "right -100px top -480px",
        "hero-3D-Circle-2-lg": "right 244px top -310px",
        "reward-competition-left-avatar": "top 0px right -4px",
        "reward-competition-right-avatar": "top 0px right -4px",
        "reward-eclipse-5": "-63px -360px",
        "reward-image-carousal": "0px 42px",
        "reward-exclusive-comp-web": "1px -75px",
        "reward-exclusive-comp-tab": "-64px -108px",
        "reward-competition-games": "3px 65px",
        "hiws-character-1": "154px 99px",
        "hiws-character-yogi": "200px 25px"
      },
      boxShadow: {
        sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "gray-10-outline-10-opacity-0.7": "0px 0px 10px rgba(255, 255, 255, 0.7)",
        "navy-60-outline-60-opacity-0.8": "0px 0px 60px rgba(42, 59, 137, 0.8)",
        "gray-90-outline-4-8-apacity-0.15": " 0px 4px 8px 0px rgba(0, 0, 0, 0.15)",
        "navy-80-image-card": "0 4px 8px 0 rgb(16 14 134), 0 6px 20px 0 rgb(0 3 10)"
      },
      fill: (theme) => ({
        current: "currentColor",
        "gray-10": theme("colors.gray.10"),
        "gray-50": theme("colors.gray.50"),
        "indigo-50": theme("colors.indigo.50"),
        "navy-60": theme("colors.navy.70"),
        "navy-80": theme("colors.navy.80"),
        "success-30": theme("colors.success.30"),
        "success-70": theme("colors.success.70")
      })
    },
    stroke: (theme) => ({
      "navy-40": theme("colors.navy.40"),
      "navy-60": theme("colors.navy.60"),
      "navy-80": theme("colors.navy.80"),
      "success-50": theme("colors.success.50"),
      "success-70": theme("colors.success.70")
    }),
    screens: {
      cardSmall: "450px"
    }
  },
  plugins: [require("@tailwindcss/aspect-ratio"), require("@tailwindcss/typography")]
});
