import "reflect-metadata";
import React from "react";
import PaymentInformation, { PaymentInformationProps } from "../../pages/payment-information";
import { aUser } from "../factories/User/User";
import { renderPage } from "../helpers/page";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";
import { mockMatchMedia } from "__tests__/helpers/window";

jest.mock("../../src/context/DependencyContext");

describe("PaymentInformation", () => {
  mockMatchMedia();
  (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
  (useDependency as jest.Mock).mockReturnValue({
    analytics: {},
    errorHandler: jest.fn(),
    configuration: {
      SUPPORTED_LOCALES: ["en-us"],
      PROGRAM_CODE: "creator_network",
      MENU_ITEMS: {
        sims: { label: "UFX", gradients: ["#2E900A", "#6FF049"] },
        affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
        creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
      }
    }
  });
  const paymentInformationProps: PaymentInformationProps = {
    user: AuthenticatedUserFactory.fromSession(aUser({ status: "ACTIVE" })),
    locale: "en-us",
    FLAG_NEW_NAVIGATION_ENABLED: true,
    FLAG_NEW_FOOTER_ENABLED: true,
    CN_LAUNCH_DATE: "2022-01-01"
  };

  it("shows payment information page", () => {
    renderPage(<PaymentInformation {...paymentInformationProps} />);

    expect(screen.getByRole("heading", { level: 3 })).toHaveTextContent(/payment-information:pageTitle/);
    expect(document.title).toBe("paymentInformation");
  });
});
