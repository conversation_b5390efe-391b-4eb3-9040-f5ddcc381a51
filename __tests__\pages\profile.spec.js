import "reflect-metadata";
import Profile from "./../../pages/profile";
import { mockMatchMedia } from "../helpers/window";
import { act, render, screen, waitFor, within } from "@testing-library/react";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import { aCreatorWithFlaggedStatus } from "../factories/creators/CreatorWithFlaggedStatus";
import { anAccountInformationWithPayableStatus } from "../factories/creators/AccountInformationWithPayableStatus";
import { useRouter } from "next/router";
import { renderPage } from "../helpers/page";
import { aConnectedAccount } from "../factories/creators/ConnectedAccounts";
import "next/config";
import userEvent from "@testing-library/user-event";
import CreatorsService, {
  CreatorWithFlaggedStatusProfile,
  CreatorWithPayableStatusProfile
} from "../../src/api/services/CreatorsService";
import ConnectedAccountsService from "../../src/api/services/ConnectedAccountsService";
import { useDependency } from "../../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { axe } from "jest-axe";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  useDetectScreen: jest.fn().mockImplementation((width) => width === 10000)
}));
jest.mock("../../src/api/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../src/api/services/CreatorsService"),
    getCreatorWithCreatorCode: jest.fn(),
    getCreatorWithPayableStatus: jest.fn(),
    getCreatorWithExpiredAccounts: jest.fn(),
    register: jest.fn(),
    update: jest.fn(),
    sendEmailToPOC: jest.fn(),
    getCreatorWithTier: jest.fn(),
    getCreatorWithFlaggedStatus: jest.fn()
  };
});
jest.mock("../../src/api/services/ConnectedAccountsService");

describe("profile", () => {
  mockMatchMedia();
  const analytics = { clickedFooterLink: jest.fn() };
  const footerLinks = [
    ["how", "/how-it-works"],
    ["perks", "/opportunities-rewards"],
    ["policy", "/trust-and-safety-guidelines"]
  ];
  const router = { locale: "en-us", query: {}, push: jest.fn() };
  const creatorName = "Jane";
  const profileProps = { user: {}, FLAG_NEW_NAVIGATION_ENABLED: false, FLAG_NEW_FOOTER_ENABLED: false };
  const metadataService = {
    getHardwarePartners: jest.fn().mockResolvedValue([]),
    getFranchises: jest.fn().mockResolvedValue([]),
    getPlatformsMatching: jest.fn().mockResolvedValue([]),
    getCreatorTypes: jest.fn().mockResolvedValue([]),
    getCountries: jest.fn().mockResolvedValue([])
  };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      analytics,
      errorHandler,
      metadataClient: {},
      creatorsClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
    MetadataService.mockReturnValue(metadataService);
    CreatorsService.getCreatorWithFlaggedStatus.mockResolvedValue({
      data: new CreatorWithFlaggedStatusProfile(
        aCreatorWithFlaggedStatus({
          accountInformation: anAccountInformationWithPayableStatus({ firstName: creatorName })
        })
      )
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: new CreatorWithPayableStatusProfile(
        aCreatorWithPayableStatus({
          accountInformation: anAccountInformationWithPayableStatus({ firstName: creatorName })
        })
      )
    });
    ConnectedAccountsService.clearFbPages.mockImplementation(() => Promise.resolve());
  });

  it("shows a creator profile information", async () => {
    const { unmount } = render(<Profile {...profileProps} />);

    await waitFor(() => {
      expect(screen.getByText(creatorName)).toBeInTheDocument();
      expect(CreatorsService.getCreatorWithFlaggedStatus).toHaveBeenCalledTimes(1);
      expect(metadataService.getHardwarePartners).toHaveBeenCalledTimes(1);
      expect(metadataService.getFranchises).toHaveBeenCalledTimes(1);
      expect(metadataService.getPlatformsMatching).toHaveBeenCalledTimes(1);
      expect(metadataService.getCreatorTypes).toHaveBeenCalledTimes(1);
      expect(metadataService.getCountries).toHaveBeenCalledTimes(2);
    });

    // Facebook pages values are cleared from session when the page unloads
    unmount();
    await waitFor(() => expect(ConnectedAccountsService.clearFbPages).toHaveBeenCalledTimes(1));
  });

  it("shows TikTok account card", async () => {
    useRouter.mockImplementation(() => ({ query: { section: "connected-accounts" } }));
    const creatorWith3Accounts = aCreatorWithPayableStatus({
      connectedAccounts: [aConnectedAccount({ type: "FACEBOOK" }, { type: "INSTAGRAM" }, { type: "YOUTUBE" })]
    });
    CreatorsService.getCreatorWithFlaggedStatus.mockResolvedValue({
      data: {
        creator: creatorWith3Accounts
      }
    });

    render(<Profile {...profileProps} />);

    expect(await screen.findAllByText("connect-accounts:addAccount")).toHaveLength(5);
    expect(await screen.findByText(/TikTok/i)).toBeInTheDocument();
  });

  it("shows toast message when TikTok scope is invalid", async () => {
    useRouter.mockImplementation(() => ({ query: { section: "connected-accounts" } }));
    const creatorWith3Accounts = aCreatorWithPayableStatus();
    CreatorsService.getCreatorWithFlaggedStatus.mockResolvedValue({
      data: {
        creator: creatorWith3Accounts
      }
    });

    const { unmount } = renderPage(<Profile {...profileProps} invalidTikTokScope />);

    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent(/unhandledError/i);
    unmount(); // close toast message
  });

  it.each(footerLinks)("logs 'Link Clicked' event when clicking on '%s' marketing page", async (name, url) => {
    const locale = "en-us";

    const { unmount } = renderPage(<Profile {...profileProps} />);

    await userEvent.click(await screen.findByRole("button", { name }));

    await waitFor(async () => {
      expect(analytics.clickedFooterLink).toHaveBeenCalledWith({ locale, url }); // Updated the expected arguments
      expect(analytics.clickedFooterLink).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith(url);
    });
    unmount();
  });

  it("logs 'Link Clicked' event when clicking on 'faqs' marketing page", async () => {
    const locale = "en-us";
    const url = "/faq";

    const { unmount } = renderPage(<Profile {...profileProps} />);

    const buttons = await screen.findAllByRole("button", { name: "faqs" });
    await userEvent.click(buttons[0]);

    await waitFor(async () => {
      expect(analytics.clickedFooterLink).toHaveBeenCalledWith({ locale, url }); // Updated the expected arguments
      expect(analytics.clickedFooterLink).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith(url);
    });
    unmount();
  });

  describe("with 'FLAG_COUNTRIES_BY_TYPE' flag enabled", () => {
    it("shows a creator profile information", async () => {
      metadataService.getCountriesMatching = jest
        .fn()
        .mockResolvedValue([{ value: "MX", label: "Mexico", name: "Mexico" }]);
      MetadataService.mockReturnValue(metadataService);
      CreatorsService.getCreatorWithFlaggedStatus.mockResolvedValue({
        data: new CreatorWithFlaggedStatusProfile(
          aCreatorWithFlaggedStatus({
            mailingAddress: { country: { code: "CA", name: "Canada" } },
            legalInformation: { country: { code: "MX", name: "Mexico" } }
          })
        )
      });

      render(<Profile {...profileProps} FLAG_COUNTRIES_BY_TYPE />);

      await waitFor(() => {
        expect(screen.getByText("Mexico")).toBeInTheDocument();
        expect(metadataService.getCountriesMatching).toHaveBeenCalledTimes(2);
        expect(metadataService.getCountriesMatching).toHaveBeenCalledWith({ type: "PAYMENTS" });
      });
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(<Profile {...profileProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
