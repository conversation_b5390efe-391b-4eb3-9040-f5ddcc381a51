import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import Registrations from "../../../../pages/opportunities/[id]/registrations";
import userEvent from "@testing-library/user-event";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import { mockMatchMedia } from "../../../helpers/window";
import { renderPage } from "../../../helpers/page";
import { useRouter } from "next/router";
import OpportunityService, { OpportunityWithDeliverables } from "../../../../src/api/services/OpportunityService";
import OperationsService from "../../../../src/api/services/OperationsService";
import OpportunityWithDeliverablesResponse from "../../../../src/opportunities/OpportunityWithDeliverables";
import { aCreator } from "../../../factories/creators/Creator";
import { useDependency } from "../../../../src/context/DependencyContext";

jest.mock("../../../../src/api/services/OpportunityService");
jest.mock("../../../../src/api/services/OperationsService");
jest.mock("../../../../src/context/DependencyContext");

describe("Join Opportunity Game Codes page", () => {
  mockMatchMedia();
  const router = { query: { id: "OPPO183", step: "game-code" }, locale: "en-us" };
  const analytics = {
    continuedJoinOpportunityFlow: jest.fn(),
    completedJoinOpportunityFlow: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    useDependency.mockReturnValue({ analytics, errorHandler: jest.fn() });
  });

  it("shows available platforms", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables();
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    expect(await screen.findAllByText(/game-code:platform/i)).toHaveLength(1);
    expect(await screen.findByText(/game-code:platform/i)).toBeInTheDocument();
  });

  it("it adds default option to regions after changing platform", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables();
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );
    const platformOptionsLabel = await screen.findAllByText(/game-code:platform/i);
    expect(platformOptionsLabel).toHaveLength(1);
    // Select first platform
    const platformOptions = await screen.findAllByRole("checkbox");
    await userEvent.click(platformOptions[0]);
    await waitFor(() => expect(platformOptions[0]).toBeChecked());
    const region = screen.getByText(/game-code:selectRegion/i);

    await userEvent.click(region);

    await waitFor(() => {
      const defaultOption = screen.getAllByText(/game-code:selectRegion/i)[1];
      expect(defaultOption).toHaveClass("select-option-label");
    });
  });

  it("it disables next button when default option is selected", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      id: "OPPO183",
      hasGameCodes: true,
      hasDeliverables: false
    });
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );
    const platformOptionsLabel = await screen.findAllByText(/game-code:platform/i);
    expect(platformOptionsLabel).toHaveLength(1);
    // Select first platform
    const platformOptions = await screen.findAllByRole("checkbox");
    await userEvent.click(platformOptions[0]);
    await waitFor(() => expect(platformOptions[0]).toBeChecked());
    const defaultRegion = screen.getByText(/game-code:selectRegion/i);

    await userEvent.click(defaultRegion);

    // Default option should be selected
    const addedDefaultOption = screen.getAllByText(/game-code:selectRegion/i)[1];
    await waitFor(() => expect(addedDefaultOption).toHaveClass("select-option-label"));
    // Button should be disabled
    await waitFor(() => expect(screen.getAllByRole("button", { name: /join/i })[1]).toBeDisabled());
  });

  it("logs 'Continued Join Opportunity Flow' event after clicking next button when region is selected", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO183",
        hasEvent: true,
        hasPayments: true,
        hasGameCodes: true,
        hasDeliverables: true,
        hasDiscordChannel: true
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );
    const platformOptionsLabel = await screen.findAllByText(/game-code:platform/i);
    expect(platformOptionsLabel).toHaveLength(1);
    // Select first platform
    const platformOptions = await screen.findAllByRole("checkbox");
    await userEvent.click(platformOptions[0]);
    await waitFor(() => expect(platformOptions[0]).toBeChecked());
    const defaultRegion = screen.getByText(/game-code:selectRegion/i);
    await userEvent.click(defaultRegion);
    // Default option should be selected
    const asiaRegion = screen.getByRole("button", { name: "Asia" });
    await userEvent.click(asiaRegion);
    const nextButton = screen.getAllByRole("button", { name: "next" })[0];
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.continuedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  it("logs 'Completed Join Opportunity Flow' event after clicking next button", async () => {
    const opportunity = OpportunityWithDeliverablesResponse.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO183",
        hasGameCodes: true,
        hasDeliverables: false
      })
    );
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    OperationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    const platformOptionsLabel = await screen.findAllByText(/game-code:platform/i);
    expect(platformOptionsLabel).toHaveLength(1);
    // Select first platform
    const platformOptions = await screen.findAllByRole("checkbox");
    await userEvent.click(platformOptions[0]);
    await waitFor(() => expect(platformOptions[0]).toBeChecked());
    const defaultRegion = screen.getByText(/game-code:selectRegion/i);
    await userEvent.click(defaultRegion);
    // Default option should be selected
    const asiaRegion = screen.getByRole("button", { name: "Asia" });
    await userEvent.click(asiaRegion);
    const joinButton = screen.getAllByRole("button", { name: /join/i })[1];
    await waitFor(() => expect(joinButton).toBeEnabled());

    await userEvent.click(joinButton);

    await waitFor(() => {
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
      expect(OperationsService.saveParticipation).toHaveBeenCalledTimes(1);
    });
  });

  it("shows game code page with opportunity details even though game code enabled with no platform and region", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      id: "OPPO183",
      hasGameCodes: true,
      title: "Fifa",
      gameCodes: null,
      platformOptions: [],
      platformRegionOptions: []
    });
    const participationStatusResponse = [{ participationId: null, status: "NA", id: "OPPO123" }];
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: participationStatusResponse
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    await waitFor(() => {
      const opportunityTitle = screen.getByText("Fifa");
      expect(opportunityTitle).toBeInTheDocument();
      const platformOptionsLabel = screen.getByText(/game-code:platform/i);
      expect(platformOptionsLabel).toBeInTheDocument();
    });
  });

  it("shows new platforms in the game code section in join opportunity flow", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      platformOptions: [
        {
          value: "a0BDF00000MrXnL2AV",
          label: "EA App"
        },
        {
          value: "a0BDF00000MrXnR2AV",
          label: "Epic Store"
        },
        {
          value: "a0BDF00000MrXnQ2AV",
          label: "Steam"
        }
      ]
    });
    OpportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    await waitFor(() => {
      expect(screen.getAllByRole("checkbox")).toHaveLength(3);
      expect(screen.getByText(/game-code:EA App/i)).toBeInTheDocument();
      expect(screen.getByText(/game-code:Steam/i)).toBeInTheDocument();
      expect(screen.getByText(/game-code:Epic Store/i)).toBeInTheDocument();
    });
  });
});
