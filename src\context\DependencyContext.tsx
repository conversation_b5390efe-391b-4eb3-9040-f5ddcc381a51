import React, { create<PERSON>ontext, Di<PERSON><PERSON>, ReactNode, useContext } from "react";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Action } from "@src/errorHandling/errorHandler";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";

const DependencyContext = createContext(undefined);

export function useDependency() {
  return useContext(DependencyContext);
}

export function DependencyProvider({
  configuration,
  errorHandler,
  metadataClient,
  notificationsClient,
  creatorsClient,
  analytics,
  applicationsClient,
  children
}: {
  configuration: Record<string, unknown>;
  errorHandler: (dispatch: Dispatch<Action>, e: Error) => void;
  metadataClient: TraceableHttpClient;
  notificationsClient: TraceableHttpClient;
  creatorsClient: TraceableHttpClient;
  analytics: BrowserAnalytics;
  applicationsClient: TraceableHttpClient;
  children: ReactNode;
}) {
  return (
    <DependencyContext.Provider
      value={{
        configuration,
        errorHandler,
        metadataClient,
        notificationsClient,
        creatorsClient,
        analytics,
        applicationsClient
      }}
    >
      {children}
    </DependencyContext.Provider>
  );
}
