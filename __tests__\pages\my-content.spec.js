import "reflect-metadata";
import { render, screen, waitFor } from "@testing-library/react";
import MyContent from "./../../pages/my-content";
import { aSubmittedContent } from "../factories/opportunities/SubmittedContent";
import { aContentFeedback } from "../factories/opportunities/ContentFeedback";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../helpers/window";
import { useTranslation } from "next-i18next";
import SubmittedContentService from "../../src/api/services/SubmittedContentService";
import { delay } from "../helpers/timer";
import { useDependency } from "../../src/context/DependencyContext";
import { aSubmittedContentWithFinalRemark } from "../factories/opportunities/SubmittedContentWithReviewFinalRemark";

jest.mock("next-i18next", () => {
  // This variable cannot be inlined or a new mock will be created per import
  const t = jest.fn().mockImplementation((str, _) => str);

  return {
    ...jest.requireActual("next-i18next"),
    useTranslation: () => ({
      t,
      i18n: {
        changeLanguage: () => new Promise(() => {})
      }
    })
  };
});
jest.mock("../../src/api/services/SubmittedContentService");
jest.mock("../../src/context/DependencyContext");

describe("MyContent", () => {
  mockMatchMedia();
  const submitContentResponse = {
    contents: [
      {
        id: "De73_632qKs",
        name: "Test submit content",
        type: "YOUTUBE",
        thumbnail: "https://i.ytimg.com/vi/De73_632qKs/default.jpg",
        status: null,
        submittedOn: 1650560236000,
        contentUri: "https://www.youtube.com/watch?v=De73_632qKs",
        contentType: "video",
        submittedDate: 1650585637000,
        opportunityName: "Apex Legends",
        opportunityId: "a0MK000000ACbyvMAD",
        requiresChanges: () => false,
        formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
      }
    ],
    count: 1,
    total: 1
  };
  const router = {
    route: "/",
    locale: "en-us",
    isReady: true,
    push: jest.fn().mockResolvedValue(true)
  };
  const contentFeedback = {
    ...aContentFeedback(),
    requiresChanges: () => true,
    formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
  };
  const content = aSubmittedContent({
    name: "Feedback provided for submitted content",
    status: "CHANGE_REQUESTED",
    id: "beta456",
    requiresChanges: () => true,
    formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
  });
  const contentWithChangesRequested = {
    ...submitContentResponse,
    contents: [content]
  };
  const myContentProps = {
    user: {},
    locale: ""
  };

  beforeEach(() => {
    jest.clearAllMocks();
    addAPICallsTo();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      analytics: {},
      configuration: { SUPPORTED_LOCALES: ["en-us"], FLAG_SIGNED_URL_V1_ENABLED: false },
      errorHandler: jest.fn(),
      FLAG_SUBMITTED_CONTENT_WITH_PROGRAM: false
    });
  });

  it("displays my content title and its description", async () => {
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: submitContentResponse
    });

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: "myContent" })).toBeInTheDocument();
      expect(screen.getByText(/dashboard:myContentDescription/i)).toBeInTheDocument();
      expect(screen.getByText("Test submit content")).toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change requested`(feedback has been provided) as per latest figma design
   */
  it("displays content with change requested", async () => {
    const content = aSubmittedContent({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      id: "beta456",
      requiresChanges: () => true, // since status = "CHANGE_REQUESTED"
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const contentWithChangesRequested = {
      ...submitContentResponse,
      contents: [content]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: contentWithChangesRequested
    });

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByText(/^changesRequired/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-beta456-unread-icon")).not.toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change received`(updated content based on feedback received) as per latest figma design
   */
  it("displays content with change received", async () => {
    const content = aSubmittedContent({
      name: "Updated content based on feedback received",
      status: "CHANGE_RECEIVED",
      id: "beta456",
      requiresChanges: () => false,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const contentWithChangesReceived = {
      ...submitContentResponse,
      contents: [content]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: contentWithChangesReceived
    });

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText("Updated content based on feedback received")).toBeInTheDocument();
      expect(screen.getByText(/inReview$/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-beta456-unread-icon")).not.toBeInTheDocument();
    });
  });

  it("displays submitted content card", async () => {
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: submitContentResponse
    });

    render(<MyContent {...myContentProps} />);

    await screen.findByText("Test submit content");
  });

  it("displays content with change requested", async () => {
    const router = {
      locale: "ja-jp",
      push: jest.fn(),
      isReady: true
    };
    useRouter.mockImplementation(() => router);
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: submitContentResponse
    });

    render(<MyContent {...myContentProps} />);
    const opportunity = await screen.findByText("Apex Legends");

    await userEvent.click(opportunity);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/a0MK000000ACbyvMAD"));
  });

  it("shows skeleton loader while feedback is being retrieved", async () => {
    addAPICallsTo();

    render(<MyContent {...myContentProps} locale={"en-us"} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(screen.getByTestId("deliverable-content-feedback-skeleton-no-animation")).toBeInTheDocument();
      expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
    });
  });

  function addAPICallsTo() {
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: contentWithChangesRequested
    });

    SubmittedContentService.getContentsFeedback.mockImplementation(async () =>
      delay(1000).then(() =>
        Promise.resolve({
          data: { contentsFeedback: [contentFeedback] }
        })
      )
    );
  }

  it("shows feedback and verify the contents for website", async () => {
    const { t } = useTranslation();
    const websiteContent = aSubmittedContent({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      type: "WEBSITE",
      id: "beta456",
      requiresChanges: () => true,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const websiteContentWithChangesRequested = {
      ...submitContentResponse,
      contents: [websiteContent]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: websiteContentWithChangesRequested
    });

    render(<MyContent {...myContentProps} locale={"en-us"} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
      expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
        contentType: "url",
        updateType: "update"
      });
      expect(screen.getByRole("button", { name: /hideChangesRequired/i })).toBeInTheDocument();
      expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    });
  });

  it("shows feedback and verify the contents for file", async () => {
    const { t } = useTranslation();
    const fileContent = aSubmittedContent({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      type: "FILE",
      id: "beta456",
      requiresChanges: () => true,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const fileContentWithChangesRequested = {
      ...submitContentResponse,
      contents: [fileContent]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: fileContentWithChangesRequested
    });

    render(<MyContent {...myContentProps} locale={"en-us"} />);
    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
      expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
        contentType: "file",
        updateType: "upload"
      });
      expect(screen.getByRole("button", { name: /hideChangesRequired/i })).toBeInTheDocument();
      expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    });
  });

  describe("with FLAG_CONTENT_WITH_FINAL_REMARK enabled", () => {
    it("displays review final remark with formatted date for rejected content", async () => {
      const content = aSubmittedContentWithFinalRemark({
        name: "Content with review remarks",
        status: "REJECTED",
        id: "beta789",
        requiresChanges: () => false,
        formattedSubmittedDate: () => "Apr 22, 2022 (IST)",
        reviewFinalRemark: {
          content: "Review comment",
          author: "Reviewer",
          date: 1650672037000
        },
        formattedReviewFinalRemarkDate: () => "Apr 23, 2022 (IST)"
      });
      const contentWithReviewRemark = {
        ...submitContentResponse,
        contents: [content]
      };
      SubmittedContentService.getSubmittedContentsFinalRemarks.mockResolvedValue({
        data: contentWithReviewRemark
      });

      render(<MyContent {...myContentProps} FLAG_CONTENT_WITH_FINAL_REMARK={true} locale={"en-us"} />);

      await waitFor(() => {
        expect(screen.getByText("Content with review remarks")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /viewDetails/i })).toBeInTheDocument();
      });

      await userEvent.click(screen.getByRole("button", { name: /viewDetails/i }));

      await waitFor(() => {
        expect(screen.getByText(/Apr 23, 2022 \(IST\)/)).toBeInTheDocument();
      });
    });
  });

  it("replaces content type to image for carousel albums", async () => {
    // we are checking the logic inside the class contructor
    const data = {
      ...submitContentResponse,
      contents: [{ ...submitContentResponse.contents[0], contentType: "image", type: "INSTAGRAM" }]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data
    });

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText(/image/i)).toBeInTheDocument();
      expect(screen.getByTestId("content-submission-icon-image")).toBeInTheDocument();
    });
  });

  describe("with 'FLAG_SUBMITTED_CONTENT_WITH_PROGRAM' enabled", () => {
    useDependency.mockReturnValue({
      configuration: { SUPPORTED_LOCALES: ["en-us"], FLAG_SIGNED_URL_V1_ENABLED: false },
      errorHandler: jest.fn(),
      FLAG_SUBMITTED_CONTENT_WITH_PROGRAM: true
    });
    const contentWithReviewRemark = {
      ...submitContentResponse,
      contents: [content]
    };
    it("shows my opportunities and submitted content ", async () => {
      SubmittedContentService.getSubmittedContentsFinalRemarksWithProgramCode.mockResolvedValue({
        data: contentWithReviewRemark
      });
      render(<MyContent {...myContentProps} />);
      await waitFor(() => {
        expect(screen.getByText((content) => content.includes("dashboard:myContentDescription"))).toBeInTheDocument();
      });
    });
  });
});
