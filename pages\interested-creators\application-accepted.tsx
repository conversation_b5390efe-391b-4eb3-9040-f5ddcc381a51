import "reflect-metadata";
import React, { ComponentType, useCallback, useEffect } from "react";
import flags from "../../utils/feature-flags";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationAcceptedProps from "@src/serverprops/ApplicationAcceptedProps";
import { ApplicationAcceptedPageLabels } from "@src/contentManagement/ApplicationAcceptedPageMapper";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { useDependency } from "@src/context/DependencyContext";

const ApplicationAcceptedPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/ApplicationAcceptedPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type ApplicationAcceptedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  locale: string;
  showInitialMessage?: boolean;
  pageLabels: ApplicationAcceptedPageLabels & CommonPageLabels;
};

export default function Accepted({ locale, pageLabels }: ApplicationAcceptedProps): JSX.Element {
  const { applicationAcceptedLabels, commonPageLabels } = pageLabels;
  const { creatorNetwork, close } = commonPageLabels;
  const { pageTitle } = applicationAcceptedLabels;
  const router = useRouter();
  const { analytics } = useDependency();
  const goToInformationPage = useCallback(() => {
    router.push(`/onboarding/information`);
  }, [router]);

  useEffect(() => {
    document.title = pageTitle;
  }, []);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <div className="mg-bg"> </div>
          <ApplicationAcceptedPage
            labels={applicationAcceptedLabels}
            locale={locale}
            analytics={analytics}
            onButtonClick={goToInformationPage}
            applicationAcceptedThumbnail={"/img/home-header--980w-x-690h.png"}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationAcceptedProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationAcceptedProps>;
  }

  const application = await withCreatorApplication(req, res);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isAccepted()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationAccepted");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      pageLabels,
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
