import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import React from "react";
import Disclosure, { DisclosureProps } from "../../pages/disclosure";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../src/context/DependencyContext");

describe("Disclosure", () => {
  mockMatchMedia();
  (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us", pathname: "/disclosure" }));
  const analytics = { viewedMarketingPage: jest.fn() };
  const disclosureProps: DisclosureProps = {
    user: AuthenticatedUserFactory.fromSession(aUser({ status: "ACTIVE" })),
    interestedCreator: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
  });

  it("shows its title and image", () => {
    renderPage(<Disclosure {...disclosureProps} />);

    expect(screen.getByText(/disclosure:title/i)).toBeInTheDocument();
    expect(screen.getByTestId("disclosure-image")).toBeInTheDocument();
  });

  it("doesn't log 'Viewed Marketing Page' on loading this page when user is not logged in", async () => {
    renderPage(<Disclosure {...disclosureProps} user={null} />);

    await waitFor(() => expect(analytics.viewedMarketingPage).not.toHaveBeenCalled());
  });

  it("logs 'Viewed Marketing Page' on loading this page", async () => {
    renderPage(<Disclosure {...disclosureProps} />);

    await waitFor(() => {
      expect(analytics.viewedMarketingPage).toHaveBeenCalledTimes(1);
      expect(analytics.viewedMarketingPage).toHaveBeenCalledWith({ locale: "en-us", page: "/disclosure" });
    });
  });
});
