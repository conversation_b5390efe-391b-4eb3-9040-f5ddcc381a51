import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Dashboard from "./../../pages/dashboard";
import {
  anOpportunityWithActivationWindow,
  aOpportunityWithPerks
} from "../factories/opportunities/OpportunityWithPerks";
import { aSubmittedContent } from "../factories/opportunities/SubmittedContent";
import { aSubmittedContentWithFinalRemark } from "../factories/opportunities/SubmittedContentWithReviewFinalRemark";
import { aContentFeedback } from "../factories/opportunities/ContentFeedback";
import { aParticipationStatus } from "../factories/opportunities/ParticipationStatus";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { renderWithToast, triggerAnimationEnd } from "../helpers/toast";
import { useAppContext } from "../../src/context";
import { onToastClose } from "../../utils";
import { useTranslation } from "next-i18next";
import { aCreatorCode } from "../factories/opportunities/CreatorCode";
import { aParticipationDetails } from "../factories/opportunities/ParticipationDetails";
import { aParticipationStatusWithSubmissionStatus } from "../factories/opportunities/ParticipationStatusWithSubmissionStatus";
import { aContentSubmission } from "../factories/opportunities/OpportunityContentSubmission";
import { anOpportunityEvent } from "../factories/opportunities/OpportunityEvent";
import "next/config";
import SubmittedContentService, {
  ContentsFeedback,
  SubmittedContentWithDeliverableDetail,
  SubmittedContentWithFinalRemark
} from "../../src/api/services/SubmittedContentService";
import OpportunityService, { OpportunityWithActivationWindow } from "../../src/api/services/OpportunityService";
import OperationsService from "../../src/api/services/OperationsService";
import { delay } from "../helpers/timer";
import NotificationsFactory from "../../src/notifications/NotificationsFactory";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../src/context/DependencyContext";
import { aPerk, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../analytics/browser/src/ampli", () => {
  return {
    ampli: {
      identify: jest.fn(),
      signedInToCreatorNetwork: jest.fn()
    }
  };
});
jest.mock("../../src/api/services/SubmittedContentService", () => {
  return {
    ...jest.requireActual("../../src/api/services/SubmittedContentService"),
    getSubmittedContents: jest.fn(),
    getContentsFeedback: jest.fn()
  };
});
jest.mock("../../src/api/services/OpportunityService", () => {
  return {
    ...jest.requireActual("../../src/api/services/OpportunityService"),
    getParticipationStatusWithSubmissionInformation: jest.fn(),
    matchingWithEventDetails: jest.fn(),
    getParticipationDetails: jest.fn(),
    matchingWithEventWitCodeDetails: jest.fn()
  };
});
jest.mock("../../src/api/services/OperationsService");
jest.mock("../../src/api/services/Client");
jest.mock("../../src/context", () => ({
  ...jest.requireActual("../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: { isLoading: false } })
}));
jest.mock("../../utils", () => ({
  ...jest.requireActual("../../utils"),
  onToastClose: jest.fn()
}));
jest.mock("next-i18next", () => {
  // This variable cannot be inlined or a new mock will be created per import
  const t = jest.fn().mockImplementation((str, _) => str);

  return {
    ...jest.requireActual("next-i18next"),
    useTranslation: () => ({
      t,
      i18n: {
        changeLanguage: () => new Promise(() => {})
      }
    })
  };
});

describe("Dashboard", () => {
  mockMatchMedia();
  const pageLabels = {
    notificationsPageLabels: {}
  };
  const router = {
    locale: "en-us",
    push: jest.fn()
  };
  const dashboardProps = {
    user: {},
    pageLabels,
    locale: "en-us"
  };
  let submittedContentResponse;
  const noOpportunities = { opportunities: [], count: 0, total: 0 };
  const invitedOpportunityId = "abc123";
  const pastOpportunityId = "past456";
  const joinedOpportunityId = "joined789";
  const invitedOpportunity = [
    new OpportunityWithActivationWindow(
      anOpportunityWithActivationWindow({
        title: "Test Invited Opportunity",
        hasDeliverables: true,
        hasGameCodes: true,
        perks: [aPerk({ code: "COLLAB", label: "Collab" })],
        id: invitedOpportunityId
      })
    )
  ];
  const participationId = "OPPO123";
  const joinedOpportunity = [
    new OpportunityWithActivationWindow(
      anOpportunityWithActivationWindow({
        title: "Test Joined Opportunity",
        id: joinedOpportunityId,
        status: "JOINED",
        hasGameCodes: true,
        hasDeliverables: true,
        perks: [aPerk({ code: "PAID", label: "Paid" })],
        registrationPeriod: {
          startDate: LocalizedDate.epochMinusDays(10),
          endDate: LocalizedDate.epochMinusDays(5),
          timeZone: "GMT"
        },
        participationId
      })
    )
  ];
  const pastOpportunity = [
    new OpportunityWithActivationWindow(
      anOpportunityWithActivationWindow({
        title: "Test Past Opportunity",
        id: pastOpportunityId,
        hasGameCodes: true,
        perks: [aPerk({ code: "COLLAB", label: "Collab" })],
        status: "PAST",
        registrationPeriod: {
          startDate: LocalizedDate.epochMinusDays(10),
          endDate: LocalizedDate.epochMinusDays(5),
          timeZone: "GMT"
        }
      })
    )
  ];
  const metadataService = {
    getPlatformsMatching: jest.fn().mockResolvedValue([aPlatform({ label: "XBOX" }), aPlatform({ label: "PC" })])
  };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        isLoading: false
      }
    });
    submittedContentResponse = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Content Submission v2 Dev",
            sourceType: "USER_DEVICE",
            type: null,
            status: "APPROVED",
            contentType: "image"
          })
        )
      ]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: submittedContentResponse });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: joinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: invitedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: pastOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: pastOpportunityId,
            participationId: null
          })
        ]
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: joinedOpportunityId, participationId })
        ]
      }
    });
    useDependency.mockReturnValue({
      analytics: {},
      metadataClient: {},
      errorHandler,
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        FLAG_SIGNED_URL_V1_ENABLED: false,
        FLAG_SUBMITTED_CONTENT_WITH_PROGRAM: false
      }
    });
    MetadataService.mockReturnValue(metadataService);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({ data: [] });
  });

  it("shows my opportunities and submitted content ", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: invitedOpportunity,
        count: 1,
        total: 1
      }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await waitFor(() => {
      expect(screen.getByText(/dashboard:title/i)).toBeInTheDocument();
      //shows my opportunities
      expect(screen.getByText(/dashboard:myOpportunities/i)).toBeInTheDocument();
      expect(screen.getByText(/dashboard:invited$/i)).toBeInTheDocument();
      expect(screen.getByText(/dashboard:joined/i)).toBeInTheDocument();
      expect(screen.getByText(/dashboard:past/i)).toBeInTheDocument();
      expect(screen.getByText("Test Invited Opportunity")).toBeInTheDocument();
      //shows my submitted content
      expect(screen.getByText(/dashboard:myContent/i)).toBeInTheDocument();
      expect(screen.getByText("Content Submission v2 Dev")).toBeInTheDocument();
    });
  });

  it("shows no available submitted content message when new content card is enabled", async () => {
    SubmittedContentService.getSubmittedContents.mockResolvedValue({
      data: { contents: [], count: 0, total: 0 }
    });

    renderPage(<Dashboard {...dashboardProps} />);

    expect(await screen.findByText(/dashboard:myContent/i)).toBeInTheDocument();
    expect(await screen.findByText(/dashboard:noSubmittedContentDesc/i)).toBeInTheDocument();
    expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledWith(3, 1);
  });

  it("shows loading component while submitted content information is being retrieved", async () => {
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        isLoading: true
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [aParticipationStatus()]
      }
    });

    renderPage(<Dashboard {...{ ...dashboardProps }} />);

    expect(screen.getByRole("img", { name: /loading/i })).toBeInTheDocument();

    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        isLoading: false
      }
    });

    await waitFor(() => {
      expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
      expect(OpportunityService.matchingWithEventDetails).toHaveBeenCalledTimes(3);
      expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(2);
      expect(screen.queryByRole("img", { name: /loading/i })).not.toBeInTheDocument();
    });
  });

  it("shows 'Approved' as default status for submitted content", async () => {
    renderPage(<Dashboard {...dashboardProps} />);

    await waitFor(() => {
      expect(screen.getByText("Content Submission v2 Dev")).toBeInTheDocument();
      expect(screen.getByTestId("content-status-icon-approved")).toHaveClass("deliverable-content-card-status-icon");
      expect(screen.getByText(/approved/i)).toBeInTheDocument();
    });
  });

  it("shows submitted content details in content card", async () => {
    renderPage(<Dashboard {...dashboardProps} />);

    await waitFor(() => {
      expect(screen.getByText("Content Submission v2 Dev")).toBeInTheDocument();
      expect(screen.getByTestId("content-submission-icon-image")).toBeInTheDocument();
      expect(screen.getByText(/approved/i)).toBeInTheDocument();
      expect(screen.getByText("File")).toBeInTheDocument();
      expect(screen.getByText(/Image/i)).toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change requested`(feedback has been provided) as per latest figma design
   */
  it("shows content card with changes requested", async () => {
    const contentWithChangesRequested = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Feedback provided for submitted content",
            status: "CHANGE_REQUESTED",
            id: "alpha123"
          })
        )
      ]
    };
    /** Additional mock,*/

    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: noOpportunities
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: noOpportunities
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      opportunities: [new OpportunityWithActivationWindow(aOpportunityWithPerks({ title: "Test Invited Opportunity" }))]
    });
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: contentWithChangesRequested });

    renderPage(<Dashboard {...dashboardProps} IMPROVED_CONTENT_CARD />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByText(/^changesRequired/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-alpha123-unread-icon")).not.toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change received`(updated content based on feedback received) as per latest figma design
   */
  it("shows content card with changes received", async () => {
    const contentWithChangesRequested = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Updated content based on feedback received",
            status: "CHANGE_RECEIVED",
            id: "alpha123"
          })
        )
      ]
    };
    /** Additional mock,*/

    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: noOpportunities
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: noOpportunities
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(aOpportunityWithPerks({ title: "Test Invited Opportunity" }))
        ]
      }
    });
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: contentWithChangesRequested });

    renderPage(<Dashboard {...dashboardProps} IMPROVED_CONTENT_CARD />);

    await waitFor(() => {
      expect(screen.getByText("Updated content based on feedback received")).toBeInTheDocument();
      expect(screen.getByText(/inReview/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-alpha123-unread-icon")).not.toBeInTheDocument();
    });
  });

  it("navigates to opportunity detail page in the same locale", async () => {
    const router = {
      locale: "ja-jp",
      push: jest.fn()
    };
    useRouter.mockImplementation(() => router);
    submittedContentResponse = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            opportunityName: "FIFA - Submit the Content",
            opportunityId: "a0MK000000BqPbiMAF"
          })
        )
      ]
    };
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: submittedContentResponse });

    renderPage(<Dashboard {...dashboardProps} />);
    const opportunity = await screen.findByText("FIFA - Submit the Content");

    await userEvent.click(opportunity);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/a0MK000000BqPbiMAF"));
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage }
    });
    const { unmount } = renderWithToast(<Dashboard {...dashboardProps} />);

    const { getByRole, findByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("unhandledError");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(await findByRole("button", { name: "close" }));

    triggerAnimationEnd(screen.getByText("unhandledError"));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("shows opportunity settings for a joined opportunity", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(10),
              endDate: LocalizedDate.epochPlusDays(10),
              timeZone: "GMT"
            },
            type: "Physical Presence"
          }),
          hasEvent: true
        })
      )
    ];

    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: eventOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: /Test Joined Opportunity/i })).toBeInTheDocument();
      // display event icon in the CTA button
      expect(screen.getByTestId("in_person_event")).toBeInTheDocument();
    });
  });

  it("shows new page of joined opportunities when clicking on page number", async () => {
    const registrationPeriod = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochMinusDays(5),
      timeZone: "GMT"
    };
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity1",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc1",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity2",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc2",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity3",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc3",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity4",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc4",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity5",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc5",
          registrationPeriod
        })
      )
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity6",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc6",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity7",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc7",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity8",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc8",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity9",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc9",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Joined Opportunity10",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc10",
          registrationPeriod
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage1,
        count: 5,
        total: 10
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage2,
        count: 5,
        total: 10
      }
    });

    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatus({ status: "JOINED", id: "abc1", participationId: "1" }),
          aParticipationStatus({ status: "JOINED", id: "abc2", participationId: "2" }),
          aParticipationStatus({ status: "JOINED", id: "abc3", participationId: "3" }),
          aParticipationStatus({ status: "JOINED", id: "abc4", participationId: "4" }),
          aParticipationStatus({ status: "JOINED", id: "abc5", participationId: "5" })
        ]
      }
    });

    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatus({ status: "JOINED", id: "abc6", participationId: "6" }),
          aParticipationStatus({ status: "JOINED", id: "abc7", participationId: "7" }),
          aParticipationStatus({ status: "JOINED", id: "abc8", participationId: "8" }),
          aParticipationStatus({ status: "JOINED", id: "abc9", participationId: "9" }),
          aParticipationStatus({ status: "JOINED", id: "abc10", participationId: "10" })
        ]
      }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(secondPage).toHaveClass("pagination-text-selected");
      expect(screen.getByRole("heading", { name: "Test Joined Opportunity6" })).toBeInTheDocument();
    });
    expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    expect(OpportunityService.matchingWithEventDetails).toHaveBeenCalledTimes(4);
    expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(3);
  });

  describe("with FLAG_CONTENT_WITH_FINAL_REMARK enabled", () => {
    it("shows skeleton loader while feedback is being retrieved", async () => {
      const contentFeedback = new ContentsFeedback(aContentFeedback());
      addDelayAPICallTo(500);
      SubmittedContentService.getContentsFeedback.mockImplementation(() =>
        delay(500).then(() =>
          Promise.resolve({
            data: { contentsFeedback: [contentFeedback] }
          })
        )
      );
      SubmittedContentService.getSubmittedContentsFinalRemarks = jest.fn().mockResolvedValue({
        data: {
          contents: [
            new SubmittedContentWithFinalRemark(
              aSubmittedContentWithFinalRemark({
                name: "Feedback provided for submitted content",
                status: "CHANGE_REQUESTED",
                id: "alpha123"
              })
            )
          ]
        }
      });

      renderPage(<Dashboard {...dashboardProps} FLAG_CONTENT_WITH_FINAL_REMARK />);
      await waitFor(() => {
        expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
      });

      await userEvent.click(screen.getByRole("button", { name: /viewChangesRequired/i }));

      await waitFor(() => {
        expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
        expect(screen.queryAllByTestId("deliverable-content-feedback-skeleton-no-animation").length).toBeGreaterThan(0);
      });
    });
  });

  function addDelayAPICallTo(timeout) {
    OpportunityService.matchingWithEventDetails.mockImplementation(() =>
      delay(timeout).then(() =>
        Promise.resolve({
          data: noOpportunities
        })
      )
    );
    OpportunityService.matchingWithEventDetails.mockImplementation(() =>
      delay(timeout).then(() =>
        Promise.resolve({
          data: noOpportunities
        })
      )
    );
    OpportunityService.matchingWithEventDetails.mockImplementation(() =>
      delay(timeout).then(() =>
        Promise.resolve({
          data: {
            opportunities: [
              new OpportunityWithActivationWindow(aOpportunityWithPerks({ title: "Test Invited Opportunity" }))
            ]
          }
        })
      )
    );
    const contentWithChangesRequested = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Feedback provided for submitted content",
            status: "CHANGE_REQUESTED",
            id: "alpha123"
          })
        )
      ]
    };
    SubmittedContentService.getSubmittedContents.mockImplementation(() =>
      delay(timeout).then(() => Promise.resolve({ data: contentWithChangesRequested }))
    );
  }

  it("shows feedback and verify the contents for website", async () => {
    const { t } = useTranslation();
    const contentFeedback = new ContentsFeedback(aContentFeedback());
    addDelayAPICallTo(500);
    const websiteContentWithChangesRequested = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Feedback provided for submitted content",
            status: "CHANGE_REQUESTED",
            type: "WEBSITE",
            id: "test123user"
          })
        )
      ]
    };

    SubmittedContentService.getContentsFeedback.mockResolvedValue({ data: { contentsFeedback: [contentFeedback] } });
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: websiteContentWithChangesRequested });

    renderPage(<Dashboard {...dashboardProps} locale={"en-us"} />);
    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(
      () => {
        expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
        expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
        expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
          contentType: "url",
          updateType: "update"
        });
        expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      },
      { timeout: 1000 }
    );
  });

  it("shows feedback and verify the contents for file", async () => {
    const { t } = useTranslation();
    const contentFeedback = new ContentsFeedback(aContentFeedback());
    addDelayAPICallTo(500);
    const fileContentWithChangesRequested = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContent({
            name: "Feedback provided for submitted content",
            status: "CHANGE_REQUESTED",
            type: "FILE",
            id: "test123user"
          })
        )
      ]
    };

    SubmittedContentService.getContentsFeedback.mockResolvedValue({ data: { contentsFeedback: [contentFeedback] } });
    SubmittedContentService.getSubmittedContents.mockResolvedValue({ data: fileContentWithChangesRequested });

    renderPage(<Dashboard {...dashboardProps} locale={"en-us"} />);
    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(
      () => {
        expect(SubmittedContentService.getContentsFeedback).toHaveBeenCalledTimes(1);
        expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
        expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
          contentType: "file",
          updateType: "upload"
        });
        expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      },
      { timeout: 1000 }
    );
  });

  it("shows opportunity settings with perks", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: invitedOpportunity,
        count: 1,
        total: 1
      }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await waitFor(() => {
      expect(screen.getAllByRole("button", { name: "Test Invited Opportunity" })).toHaveLength(2);
      expect(screen.getByText(/opportunities:gameCode/)).toBeInTheDocument();
      expect(screen.getByText(/opportunities:contentSubmission$/)).toBeInTheDocument();
      expect(screen.getByText(/dashboard:invited$/)).toBeInTheDocument();
      expect(screen.getByTestId("opportunity-card-status-pill-icon-invited")).toBeInTheDocument();
      expect(screen.getByTestId("opportunity-cardv2-description")).toBeInTheDocument();
      expect(screen.getByRole("heading", { name: /opportunities:perks/ })).toBeInTheDocument();
      expect(screen.getByText(/opportunities:collab/)).toBeInTheDocument();
    });
  });

  it("shows registration window as closed in opportunity settings for a past opportunity", async () => {
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    await waitFor(() => {
      expect(screen.getAllByRole("button", { name: "Test Past Opportunity" })).toHaveLength(2);
      expect(screen.getByText(/opportunities:gameCode/)).toBeInTheDocument();
      expect(screen.getByText(/dashboard:past$/)).toBeInTheDocument();
      expect(screen.getByText(/opportunities:closed$/)).toBeInTheDocument();
      expect(screen.getByTestId("opportunity-card-status-pill-icon-past")).toBeInTheDocument();
      expect(screen.getByTestId("opportunity-cardv2-description")).toBeInTheDocument();
      expect(screen.getByRole("heading", { name: /opportunities:perks/ })).toBeInTheDocument();
      expect(screen.getByText(/opportunities:collab/)).toBeInTheDocument();
    });
  });

  it("navigates to opportunity details page when clicked on an invited opportunity card", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: invitedOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    const opportunityButton = (await screen.findAllByRole("button", { name: "Test Invited Opportunity" }))[0];

    await userEvent.click(opportunityButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith(`/opportunities/${invitedOpportunityId}`));
  });

  it("navigates to opportunity details page when clicked on a past opportunity card", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: pastOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const opportunityButton = (await screen.findAllByRole("button", { name: "Test Past Opportunity" }))[0];

    await userEvent.click(opportunityButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith(`/opportunities/${pastOpportunityId}`));
  });

  it("shows new page of invited opportunities when clicking on page number", async () => {
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity1",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc1"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity2",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc2"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity3",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc3"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity4",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc4"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity5",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc5"
        })
      )
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity6",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc6"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity7",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc7"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity8",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc8"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity9",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc9"
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Invited Opportunity10",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc10"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage1,
        count: 5,
        total: 10
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage2,
        count: 5,
        total: 10
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(secondPage).toHaveClass("pagination-text-selected");
      expect(screen.getAllByRole("button", { name: "Test Invited Opportunity6" })).toHaveLength(2);
    });
    expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    expect(OpportunityService.matchingWithEventDetails).toHaveBeenCalledTimes(4);
    expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(2);
  });

  it("shows new page of past opportunities when clicking on page number", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [],
        count: 1,
        total: 1
      }
    });

    const registrationPeriod = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochMinusDays(5),
      timeZone: "GMT"
    };
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity1",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc1",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity2",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc2",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity3",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc3",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity4",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc4",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity5",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc5",
          registrationPeriod
        })
      )
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity6",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc6",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity7",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc7",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity8",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc8",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity9",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc9",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity10",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc10",
          registrationPeriod
        })
      )
    ];

    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage1,
        count: 5,
        total: 10
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage2,
        count: 5,
        total: 10
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc1", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc2", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc3", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc4", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc5", participationId: null })
        ]
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc6", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc7", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc8", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc9", participationId: null }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: "abc10", participationId: null })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
      expect(OpportunityService.matchingWithEventDetails).toHaveBeenCalledTimes(4);
      expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(3);
      expect(secondPage).toHaveClass("pagination-text-selected");
    });
  });

  it("shows all perks for an invited opportunity when user clicks more button", async () => {
    const updatedInvitedOpportunity = [...invitedOpportunity];
    updatedInvitedOpportunity[0].perks = [
      aPerk({ value: "COLLAB", label: "Collab" }),
      aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
      aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
      aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
      aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
      aPerk({ value: "FOOD", label: "Food" }),
      aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
      aPerk({ value: "HOTEL", label: "Hotel" }),
      aPerk({ value: "PAID", label: "Paid" }),
      aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
      aPerk({ value: "SWAG", label: "Swag" }),
      aPerk({ value: "TRAVEL", label: "Travel" })
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedInvitedOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    const modalDialog = await screen.findByRole("dialog");
    const { getByRole, getAllByRole } = within(modalDialog);
    expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    expect(getAllByRole("listitem").length).toBe(updatedInvitedOpportunity[0].perks.length);
  });

  it("closes the more perks modal when user clicks close button", async () => {
    const updatedInvitedOpportunity = [...invitedOpportunity];
    updatedInvitedOpportunity[0].perks = [
      aPerk({ value: "COLLAB", label: "Collab" }),
      aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
      aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
      aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
      aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
      aPerk({ value: "FOOD", label: "Food" }),
      aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
      aPerk({ value: "HOTEL", label: "Hotel" }),
      aPerk({ value: "PAID", label: "Paid" }),
      aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
      aPerk({ value: "SWAG", label: "Swag" }),
      aPerk({ value: "TRAVEL", label: "Travel" })
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedInvitedOpportunity,
        count: 1,
        total: 1
      }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });
    await userEvent.click(moreButton);
    const closeButton = (await screen.findAllByRole("button", { name: /close$/ }))[0];

    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /allPerks/ })).not.toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /close$/ })).not.toBeInTheDocument();
    });
  });

  it("shows all perks for a past opportunity when user clicks more button", async () => {
    const perksList = [
      aPerk({ value: "COLLAB", label: "Collab" }),
      aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
      aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
      aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
      aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
      aPerk({ value: "FOOD", label: "Food" }),
      aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
      aPerk({ value: "HOTEL", label: "Hotel" }),
      aPerk({ value: "PAID", label: "Paid" }),
      aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
      aPerk({ value: "SWAG", label: "Swag" }),
      aPerk({ value: "TRAVEL", label: "Travel" })
    ];
    const registrationPeriod = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochMinusDays(5),
      timeZone: "GMT"
    };
    const updatedPastOpportunity = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity Perks",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: perksList,
          id: pastOpportunityId,
          registrationPeriod,
          status: "PAST"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedPastOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: pastOpportunityId,
            participationId: null
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    const modalDialog = await screen.findByRole("dialog");
    const { getByRole, getAllByRole } = within(modalDialog);
    expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    expect(getAllByRole("listitem").length).toBe(updatedPastOpportunity[0].perks.length);
  });

  it("closes the more perks modal when user clicks close button for past opportunity", async () => {
    const perksList = [
      aPerk({ value: "COLLAB", label: "Collab" }),
      aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
      aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
      aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
      aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
      aPerk({ value: "FOOD", label: "Food" }),
      aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
      aPerk({ value: "HOTEL", label: "Hotel" }),
      aPerk({ value: "PAID", label: "Paid" }),
      aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
      aPerk({ value: "SWAG", label: "Swag" }),
      aPerk({ value: "TRAVEL", label: "Travel" })
    ];
    const registrationPeriod = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochMinusDays(5),
      timeZone: "GMT"
    };
    const updatedPastOpportunity = [
      new OpportunityWithActivationWindow(
        aOpportunityWithPerks({
          title: "Test Past Opportunity Perks",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: perksList,
          id: pastOpportunityId,
          registrationPeriod,
          status: "PAST"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedPastOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: pastOpportunityId,
            participationId: null
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });
    await userEvent.click(moreButton);
    const closeButton = (await screen.findAllByRole("button", { name: /close$/ }))[0];

    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /allPerks/ })).not.toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /close$/ })).not.toBeInTheDocument();
    });
  });

  it("hides the perks and shows setting action buttons for a joined opportunity card", async () => {
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    await waitFor(() => {
      expect(screen.queryByText("Paid")).not.toBeInTheDocument();
      expect(screen.getByTestId("opportunity-cardv2-settings-action-buttons")).toBeInTheDocument();
    });
  });

  it("navigates to opportunity details page when clicked on a joined opportunity card", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: joinedOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    const opportunityButton = (await screen.findAllByRole("button", { name: /Test Joined Opportunity/ }))[0];

    await userEvent.click(opportunityButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith(`/opportunities/${joinedOpportunityId}`));
  });

  it("shows new page of joined opportunities when clicking on page number", async () => {
    const registrationPeriod = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochMinusDays(5),
      timeZone: "GMT"
    };
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity1",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc1",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity2",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc2",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity3",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc3",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity4",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc4",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity5",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc5",
          registrationPeriod
        })
      )
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity6",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc6",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity7",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc7",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity8",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc8",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity9",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc9",
          registrationPeriod
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Opportunity10",
          hasDeliverables: true,
          hasGameCodes: true,
          perks: [aPerk({ code: "COLLAB", label: "Collab" })],
          id: "abc10",
          registrationPeriod
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage1,
        count: 5,
        total: 10
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: opportunityPage2,
        count: 5,
        total: 10
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc1", participationId: "1" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc2", participationId: "2" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc3", participationId: "3" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc4", participationId: "4" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc5", participationId: "5" })
        ]
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: []
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc6", participationId: "6" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc7", participationId: "7" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc8", participationId: "8" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc9", participationId: "9" }),
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: "abc10", participationId: "10" })
        ]
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: []
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(screen.getAllByRole("button", { name: "Test Joined Opportunity6" })).toHaveLength(2);
      expect(secondPage).toHaveClass("pagination-text-selected");
    });
    expect(SubmittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1);
    expect(OpportunityService.matchingWithEventDetails).toHaveBeenCalledTimes(4);
    expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(3);
  });

  it("navigates to 'Deliverables' tab, for which deliverables is enabled when clicked on a 'Content Submission' button", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: joinedOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const contentSubmissionButton = await screen.findByRole("button", { name: /opportunities:contentSubmission/i });

    await userEvent.click(contentSubmissionButton);

    await waitFor(() =>
      expect(router.push).toHaveBeenCalledWith(`/opportunities/${joinedOpportunityId}?tab=content-deliverables`)
    );
  });

  it("hides the perks and shows setting action buttons for a completed opportunity card", async () => {
    const completedOpportunityId = "completed123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Completed Opportunity",
              id: completedOpportunityId,
              hasGameCodes: true,
              hasDeliverables: false,
              hasEvent: false,
              status: "COMPLETED",
              perks: [aPerk({ code: "COLLAB", label: "Collab" })],
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: completedOpportunityId,
            participationId: "abc2356"
          })
        ]
      }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    await waitFor(() => {
      expect(screen.queryByText("Collab")).not.toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: "Test Completed Opportunity" })).toHaveLength(2);
      expect(screen.getByTestId("opportunity-cardv2-settings-action-buttons")).toBeInTheDocument();
    });
  });

  it("hides opportunity setting action buttons for a past opportunity", async () => {
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    await waitFor(() =>
      expect(screen.queryByTestId("opportunity-cardv2-settings-action-buttons")).not.toBeInTheDocument()
    );
  });

  it("hides the description for a completed opportunity card", async () => {
    const completedOpportunityId = "completed123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Completed Opportunity",
              id: completedOpportunityId,
              hasGameCodes: true,
              hasDeliverables: false,
              status: "COMPLETED",
              hasEvent: false,
              perks: [aPerk({ code: "COLLAB", label: "Collab" })],
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: completedOpportunityId,
            participationId: "abc2356"
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    await waitFor(() => expect(screen.queryByTestId("opportunity-cardv2-description")).not.toBeInTheDocument());
  });

  it("shows creator code details modal for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined SAC Opportunity",
          hasDeliverables: false,
          hasGameCodes: false,
          hasEvent: false,
          perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
          id: supportACreatorOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId: "test124"
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: invitedOpportunityId,
            participationId: null
          }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: pastOpportunityId, participationId: null })
        ]
      }
    });
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:creatorCode.title/i })).toBeInTheDocument();
    expect(getByTestId("creator-code-window-details")).toBeInTheDocument();
    expect(getByText(/Apex Legends/i)).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("gets the creator code details for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined SAC Opportunity",
          hasDeliverables: false,
          hasGameCodes: false,
          hasEvent: false,
          perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
          id: supportACreatorOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId: "test124"
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: invitedOpportunityId,
            participationId: null
          }),
          aParticipationStatusWithSubmissionStatus({ status: "INVITED", id: pastOpportunityId, participationId: null })
        ]
      }
    });
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: null }
    });
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    OpportunityService.getParticipationDetails.mockResolvedValue({
      data: {
        participationStatuses: participationDetailsResponse
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledWith(supportACreatorOpportunityId);
    });
  });

  it("shows error page when getting a creator code details throws an error", async () => {
    const participationId = "test124";
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined SAC Opportunity",
          hasDeliverables: false,
          hasGameCodes: false,
          hasEvent: false,
          perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
          id: supportACreatorOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: invitedOpportunityId,
            participationId: null
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: pastOpportunityId,
            participationId: null
          })
        ]
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: []
    });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { creatorCodeDetails: null }
    });
    OpportunityService.getParticipationDetails.mockRejectedValue({
      data: []
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(3);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("hides the creator code details modal for a joined opportunity when user clicks close button", async () => {
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined SAC Opportunity",
          hasDeliverables: false,
          hasGameCodes: false,
          hasEvent: false,
          perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
          id: supportACreatorOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends"
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId: "test124"
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: invitedOpportunityId,
            participationId: null
          }),
          aParticipationStatusWithSubmissionStatus({
            status: "INVITED",
            id: pastOpportunityId,
            participationId: null
          })
        ]
      }
    });
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });
    await userEvent.click(creatorCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:creatorCode.title/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("creator-code-window-details")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows game code details modal for a joined opportunity when user clicks 'Game code' button", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "CLAIMED"
          }
        }
      ]
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });

    await userEvent.click(gameCodeButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:getGameCode/i })).toBeInTheDocument();
    expect(getByTestId("content-submission-game-code-wrapper")).toBeInTheDocument();
    expect(getByText(/Apex Legends/i)).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("hides game code details modal for a joined opportunity when user clicks close button", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const participationId = "test123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined SAC Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
          id: gameCodeOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:getGameCode/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-submission-game-code-wrapper")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows error page when getting platforms throws an error", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [] }
    });
    const participationId = "test123";
    const platformId = "platform123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    metadataService.getPlatformsMatching = jest.fn().mockRejectedValue([]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "CLAIMED"
          }
        }
      ]
    });

    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    await waitFor(() => {
      expect(metadataService.getPlatformsMatching).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(3);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows error page when getting game codes throws an error", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [] }
    });
    const participationId = "test123";
    const platformId = "platform123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          creatorCodeActivationWindow,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockRejectedValue({
      response: {
        data: []
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    await waitFor(() => {
      expect(OperationsService.viewAssignedGameCodes).toHaveBeenCalledTimes(2);
      expect(errorHandler).toHaveBeenCalledTimes(4);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows active status in 'Game Code' button for an opportunity with game codes assigned", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    expect(await screen.findByTestId("quick-navigation-active-button-game_code")).toBeInTheDocument();
  });

  it("hides active status in 'Game Code' button for an opportunity if the creator claims the game code", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.resolve());
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(gameCodeButton).not.toHaveProperty("data-testid", "quick-navigation-active-button-game_code");
      expect(screen.getByRole("button", { name: /opportunities:gameCode/ })).toBeInTheDocument();
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
    });
  });

  it("shows active status in 'Content Submission' button for an opportunity, where community manager has requested changes", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [],
        count: 1,
        total: 1
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Opportunity #222",
              id: "222",
              hasDeliverables: true,
              hasDeliverables: true,
              hasGameCodes: false
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: "222",
            participationId: "1",
            hasChangesRequested: true
          })
        ]
      }
    });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeContentSubmission: { ["222"]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows active status in 'Content Submission' button for an opportunity, where creator hasn't submitted content & window hasn't passed", async () => {
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [],
        count: 1,
        total: 1
      }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Opportunity #222",
              id: "222",
              hasDeliverables: true,
              contentSubmission: aContentSubmission({
                submissionWindow: {
                  end: LocalizedDate.epochMinusDays(4)
                }
              }),
              hasDeliverables: true,
              hasGameCodes: false
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: "222",
            participationId: "1",
            hasChangesRequested: true
          })
        ]
      }
    });

    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeContentSubmission: { ["222"]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows error page when claiming a game code throws an error", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const dispatch = jest.fn();
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Joined Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId
        })
      )
    ];
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.reject());
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const activeGameCodeButton = await screen.findByTestId("quick-navigation-active-button-game_code");

    await userEvent.click(activeGameCodeButton);

    await waitFor(() => {
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(3);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows game code details modal for a completed opportunity when user clicks 'Game code' button", async () => {
    const gameCodeOpportunityId = "GAME_123";
    const participationId = "test123";
    const platformId = "platform123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test GameCode For Completed Opportunity",
              id: gameCodeOpportunityId,
              hasGameCodes: true,
              hasDeliverables: false,
              status: "COMPLETED",
              hasEvent: false,
              perks: [aPerk({ code: "COLLAB", label: "Collab" })],
              gameTitle: "Apex Legends",
              gameCode: {
                platform: "PLAYSTATION",
                status: "CLAIMED"
              },
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([
        aPlatform({ label: "PlayStation", value: platformId }),
        aPlatform({ label: "PC", value: "aBhkf" })
      ]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "CLAIMED"
          }
        }
      ]
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    // display platform icon in the card
    expect(await screen.findByTestId("playstation")).toBeInTheDocument();
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });

    await userEvent.click(gameCodeButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:getGameCode/i })).toBeInTheDocument();
    expect(getByTestId("content-submission-game-code-wrapper")).toBeInTheDocument();
    expect(getByText(/Apex Legends/i)).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("hides game code details modal for a completed opportunity when user clicks close button", async () => {
    const gameCodeOpportunityId = "GAME_123";
    const participationId = "test123";
    const platformId = "platform123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test GameCode For Completed Opportunity",
              id: gameCodeOpportunityId,
              hasGameCodes: true,
              hasDeliverables: false,
              status: "COMPLETED",
              hasEvent: false,
              perks: [aPerk({ code: "COLLAB", label: "Collab" })],
              gameTitle: "Apex Legends",
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "CLAIMED"
          }
        }
      ]
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:getGameCode/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-submission-game-code-wrapper")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows creator code details modal for a completed opportunity when user clicks 'Creator code' button", async () => {
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Creator Code For Completed Opportunity",
              id: supportACreatorOpportunityId,
              hasGameCodes: false,
              hasDeliverables: false,
              status: "COMPLETED",
              hasEvent: false,
              perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
              gameTitle: "Apex Legends",
              creatorCodeActivationWindow,
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "CREATORCODE",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    // display creator code icon in the CTA button
    expect(await screen.findByTestId("creator_code")).toBeInTheDocument();
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:creatorCode.title/i })).toBeInTheDocument();
    expect(getByTestId("creator-code-window-details")).toBeInTheDocument();
    expect(getByText(/Apex Legends/i)).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("hides creator code details modal for a completed opportunity when user clicks close button in the modal", async () => {
    const supportACreatorOpportunityId = "SAC123";
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Creator Code For Completed Opportunity",
              id: supportACreatorOpportunityId,
              hasGameCodes: false,
              hasDeliverables: false,
              hasEvent: false,
              status: "COMPLETED",
              perks: [aPerk({ code: "CREATOR_CODE", name: "Creator Code" })],
              gameTitle: "Apex Legends",
              creatorCodeActivationWindow,
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              }
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: supportACreatorOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "CREATORCODE",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    // display creator code icon in the CTA button
    expect(await screen.findByTestId("creator_code")).toBeInTheDocument();
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });
    await userEvent.click(creatorCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:creatorCodePerk/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-submission-game-code-wrapper")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows in-person event details modal for a joined opportunity when user clicks 'Event' button", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Opportunity with In-person event",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(10),
              endDate: LocalizedDate.epochPlusDays(10),
              timeZone: "GMT"
            },
            type: "Physical Presence"
          }),
          hasEvent: true
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: eventOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("in_person_event")).toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:inPersonEvent/i })).toBeInTheDocument();
    expect(getByTestId("event-detail-modal-window-details")).toBeInTheDocument();
    expect(getByText("Hyderabad, India")).toBeInTheDocument();
    expect(getByRole("heading", { name: /opportunities:eventDetails:eventLocation/i })).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("hides in-person event details modal for a joined opportunity when user clicks close button in the modal", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Opportunity with In-person event",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(50),
              endDate: LocalizedDate.epochMinusDays(1),
              timeZone: "GMT"
            },
            type: "Physical Presence"
          }),
          hasEvent: true
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: eventOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("in_person_event")).toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });
    await userEvent.click(eventButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:inPersonEvent/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("event-detail-modal-window-details")).not.toBeInTheDocument();
    });
  });

  it("shows message when event window is closed for joined opportunities", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Opportunity with In-person event",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(50),
              endDate: LocalizedDate.epochMinusDays(10),
              timeZone: "GMT"
            },
            type: "Physical Presence"
          }),
          hasEvent: true
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: eventOpportunityId,
            participationId: "test124"
          })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    const modalDialog = await screen.findByRole("dialog");
    const { getByRole, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:eventDetails:info/i })).toBeInTheDocument();
    expect(getByText(/opportunities:remoteEvent.description/i)).toBeInTheDocument();
  });

  it("shows event details modal when 'Event' button is clicked for completed opportunities", async () => {
    const eventOpportunityId = "Event_123";
    const participationId = "test123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "event details for completed opportunity",
              id: eventOpportunityId,
              hasGameCodes: false,
              hasDeliverables: false,
              status: "COMPLETED",
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              },
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(20),
                  endDate: LocalizedDate.epochMinusDays(10),
                  timeZone: "GMT"
                },
                type: "Remote Event"
              }),
              hasEvent: true
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: eventOpportunityId, participationId })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("online_event")).toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    const modalDialog = await screen.findByRole("dialog");
    const { getByRole, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:remote/i })).toBeInTheDocument();
    expect(getByRole("heading", { name: /opportunities:eventDetails:info/i })).toBeInTheDocument();
    expect(getByText(/opportunities:remoteEvent.description/i)).toBeInTheDocument();
    expect(getByRole("button", { name: /opportunities:remoteEvent.joinEvent/ })).toBeInTheDocument();
  });

  it("hides event details modal when close button is clicked for completed opportunities", async () => {
    const eventOpportunityId = "Event_123";
    const participationId = "test123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Event for completed opportunity",
              id: eventOpportunityId,
              hasGameCodes: false,
              hasDeliverables: false,
              status: "COMPLETED",
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              },
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(20),
                  endDate: LocalizedDate.epochMinusDays(10),
                  timeZone: "GMT"
                },
                type: "Physical Presence"
              }),
              hasEvent: true
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: eventOpportunityId, participationId })
        ]
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("in_person_event")).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: /opportunities:remoteEvent.joinEvent/ })).not.toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });
    await userEvent.click(eventButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:inPersonEvent/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("event-detail-modal-window-details")).not.toBeInTheDocument();
    });
  });

  it("shows active status in 'Content Submission' where community manager has requested changes in completed opportunity", async () => {
    const contentSubmissionOpportunityId = "Test123";
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Completed Opportunity",
              id: contentSubmissionOpportunityId,
              hasGameCodes: false,
              hasDeliverables: true,
              status: "COMPLETED",
              hasEvent: false,
              perks: [aPerk()],
              registrationPeriod: {
                startDate: LocalizedDate.epochMinusDays(10),
                endDate: LocalizedDate.epochMinusDays(5),
                timeZone: "GMT"
              },
              contentSubmission: aContentSubmission({
                submissionWindow: {
                  startDate: LocalizedDate.epochMinusDays(5),
                  endDate: LocalizedDate.epochMinusDays(3),
                  timeZone: "GMT"
                }
              })
            })
          )
        ],
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({
            status: "JOINED",
            id: contentSubmissionOpportunityId,
            participationId: "test124",
            hasChangesRequested: true
          })
        ]
      }
    });

    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeContentSubmission: { [contentSubmissionOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows active status in 'Game Code' button when game code assigned for completed opportunity", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedPastOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Active Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          status: "COMPLETED",
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId,
          registrationPeriod: {
            startDate: LocalizedDate.epochMinusDays(10),
            endDate: LocalizedDate.epochMinusDays(5),
            timeZone: "GMT"
          }
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedPastOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);

    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));

    expect(await screen.findByTestId("quick-navigation-active-button-game_code")).toBeInTheDocument();
  });

  it("hides active status in 'Game Code' button when creator claims the game code in completed opportunities", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedPastOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Claim Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          status: "COMPLETED",
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId,
          registrationPeriod: {
            startDate: LocalizedDate.epochMinusDays(10),
            endDate: LocalizedDate.epochMinusDays(5),
            timeZone: "GMT"
          }
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedPastOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.resolve());
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(gameCodeButton).not.toHaveProperty("data-testid", "quick-navigation-active-button-game_code");
      expect(screen.getByRole("button", { name: /opportunities:gameCode/ })).toBeInTheDocument();
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
    });
  });

  it("shows error page when claiming a game code throws an error in a past opportunity", async () => {
    const gameCodeOpportunityId = "GAME_FAST";
    const participationId = "test123";
    const platformId = "platform123";
    const updatedPastOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Claim Game Code Opportunity",
          hasDeliverables: false,
          hasGameCodes: true,
          hasEvent: false,
          status: "COMPLETED",
          perks: [aPerk({ code: "PAID", name: "Paid" })],
          id: gameCodeOpportunityId,
          gameTitle: "Apex Legends",
          participationId,
          registrationPeriod: {
            startDate: LocalizedDate.epochMinusDays(10),
            endDate: LocalizedDate.epochMinusDays(5),
            timeZone: "GMT"
          }
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedPastOpportunity,
        count: 1,
        total: 1
      }
    });
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ value: "PC" })]);
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: {
        participationStatuses: [
          aParticipationStatusWithSubmissionStatus({ status: "JOINED", id: gameCodeOpportunityId, participationId })
        ]
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.reject());
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-past-tab"));
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });

    await userEvent.click(gameCodeButton);

    await waitFor(() => {
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(3);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows remote event details modal when 'Event' button is clicked for joined opportunities", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Opportunity with remote event",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(10),
              endDate: LocalizedDate.epochPlusDays(10),
              timeZone: "GMT"
            },
            meetingPassword: "Password",
            type: "Remote Event"
          }),
          hasEvent: true
        })
      )
    ];
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: [
        aParticipationStatusWithSubmissionStatus({
          status: "JOINED",
          id: eventOpportunityId,
          participationId: "test124"
        })
      ]
    });
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("online_event")).toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    const modalDialog = await screen.findByRole("dialog");
    expect(modalDialog).toBeInTheDocument();
    const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
    expect(getByRole("heading", { name: /opportunities:remote/i })).toBeInTheDocument();
    expect(getByTestId("event-detail-modal-window-details")).toBeInTheDocument();
    expect(getByRole("heading", { name: /opportunities:eventDetails:password/i })).toBeInTheDocument();
    expect(getByText("Password")).toBeInTheDocument();
    expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
  });

  it("hides remote event details modal when 'Event' button is clicked for joined opportunities", async () => {
    const eventOpportunityId = "aV450000345bc";
    const updatedJoinedOpportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          title: "Test Opportunity with remote event",
          hasDeliverables: false,
          hasGameCodes: false,
          id: eventOpportunityId,
          type: "marketing_opportunity",
          event: anOpportunityEvent({
            eventPeriod: {
              startDate: LocalizedDate.epochMinusDays(50),
              endDate: LocalizedDate.epochMinusDays(1),
              timeZone: "GMT"
            },
            type: "Remote Event"
          }),
          hasEvent: true
        })
      )
    ];
    OpportunityService.matchingWithEventDetails.mockResolvedValue({
      data: {
        opportunities: updatedJoinedOpportunity,
        count: 1,
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: [
        aParticipationStatusWithSubmissionStatus({
          status: "JOINED",
          id: eventOpportunityId,
          participationId: "test124"
        })
      ]
    });

    renderPage(<Dashboard {...dashboardProps} />);
    await userEvent.click(screen.getByTestId("dashboard-opportunities-joined-tab"));
    // display event icon in the CTA button
    expect(await screen.findByTestId("online_event")).toBeInTheDocument();
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });
    await userEvent.click(eventButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:remote/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("event-detail-modal-window-details")).not.toBeInTheDocument();
    });
  });
});
