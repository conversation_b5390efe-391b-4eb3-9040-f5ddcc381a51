import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import HowItWorks from "./../../pages/how-it-works";
import { aUser } from "../factories/User/User";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../helpers/window";
import "next/config";
import { useDependency } from "../../src/context/DependencyContext";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { analytics } from "googleapis/build/src/apis/analytics";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");

describe("HowItWorks", () => {
  mockMatchMedia();
  const creatorTypes = [aCreatorType()];
  const user = aUser({ status: "ACTIVE" });
  const analytics = { viewedMarketingPage: jest.fn() };
  useRouter.mockImplementation(() => ({ locale: "en-us", pathname: "/how-it-works" }));
  const metadataService = {
    getCreatorTypes: jest.fn().mockResolvedValue(creatorTypes)
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useDependency.mockReturnValue({
      analytics,
      metadataClient: {},
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
    MetadataService.mockReturnValue(metadataService);
  });

  it("shows creator types from API call", async () => {
    renderPage(<HowItWorks />);

    await waitFor(() => {
      expect(metadataService.getCreatorTypes).toBeCalledTimes(1);
      expect(screen.getAllByText(/how:title/i)[0]).toBeInTheDocument();
      expect(screen.getByText(/how:creatorTitle/i)).toBeInTheDocument();
      expect(screen.getByTestId("creator-type-id")).toBeInTheDocument();
    });
  });

  it("doesn't log 'Viewed Marketing Page' on loading this page when user is not logged in", async () => {
    renderPage(<HowItWorks />);

    await waitFor(() => expect(analytics.viewedMarketingPage).not.toHaveBeenCalled());
  });

  it("logs 'Viewed Marketing Page' on loading this page", async () => {
    renderPage(<HowItWorks user={user} />);

    await waitFor(() => {
      expect(analytics.viewedMarketingPage).toHaveBeenCalledTimes(1);
      expect(analytics.viewedMarketingPage).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/how-it-works"
      });
    });
  });

  it("shows apply button when interested creator flag is enabled", async () => {
    renderPage(<HowItWorks interestedCreator={true} />);

    await screen.findByRole("link", { name: /applyNow/i });
  });
});
