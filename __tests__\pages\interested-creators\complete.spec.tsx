import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import ApplicationComplete from "pages/interested-creators/complete";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationCompletePageLabels } from "@src/contentManagement/ApplicationCompletePageMapper";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";

describe("InterestedCreatorsComplete", () => {
  const locale = "en-us";
  const pageLabels = {
    applicationCompletePageLabels: {
      title: "Application Complete",
      description: "Thank you for your submission",
      buttonText: "Back to Home"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationCompletePageLabels & CommonPageLabels;
  const interestedCreator = {
    ...anInitialInterestedCreator(),
    nucleusId: 12345,
    contentLanguages: [{ code: "en", name: "English" }],
    createdDate: "2023-12-12T10:00:00Z",
    originEmail: "<EMAIL>"
  };

  const applicationCompleteProps = {
    locale,
    interestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
  });

  it("shows remote complete component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
