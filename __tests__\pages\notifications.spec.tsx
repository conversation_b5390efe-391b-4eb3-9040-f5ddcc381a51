import "reflect-metadata";
import { render, screen } from "@testing-library/react";
import React from "react";
import Notifications from "../../pages/notifications";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { useRouter } from "next/router";
import BrowserAnalytics, { AuthenticatedUserFactory } from "../../src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../src/context/DependencyContext");

describe("notifications", () => {
  const analytics = {} as unknown as BrowserAnalytics;
  mockMatchMedia();
  const user = AuthenticatedUserFactory.fromSession(aUser({ status: "ACTIVE" }));
  const pageLabels = {
    notificationsPageLabels: {},
    notificationsBellLabels: {}
  };
  const router = {
    locale: "en-us",
    push: jest.fn().mockResolvedValue(true)
  };
  const notificationsClient = {
    baseUrl: "https://localhost:3001",
    timeoutInMilliseconds: 15_000,
    serviceName: "cn-notifications-mfe"
  };
  const notificationsProps = { user, pageLabels, notificationsClient };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      notificationsClient: {},
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
  });

  it("shows remote notification component", async () => {
    render(<Notifications {...notificationsProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument(); // remote component should be in the page
  });
});
