import "reflect-metadata";
import { waitFor } from "@testing-library/react";
import React from "react";
import OpportunitiesRewards from "../../pages/opportunities-rewards";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { useDependency } from "../../src/context/DependencyContext";
import { analytics } from "googleapis/build/src/apis/analytics";

jest.mock("../../src/context/DependencyContext");

describe("OpportunitiesRewards", () => {
  mockMatchMedia();
  const analytics = { viewedMarketingPage: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({ locale: "en-us", pathname: "/opportunities-rewards" }));
    useDependency.mockReturnValue({ analytics, configuration: { SUPPORTED_LOCALES: ["en-us"] } });
  });

  it("doesn't log 'Viewed Marketing Page' on loading this page when user is not logged in", async () => {
    renderPage(<OpportunitiesRewards user={null} interestedCreator={true} />);

    await waitFor(() => expect(analytics.viewedMarketingPage).not.toHaveBeenCalled());
  });

  it("logs 'Viewed Marketing Page' on loading this page", async () => {
    renderPage(<OpportunitiesRewards user={aUser({ status: "ACTIVE" })} interestedCreator={true} />);

    await waitFor(() => {
      expect(analytics.viewedMarketingPage).toHaveBeenCalledTimes(1);
      expect(analytics.viewedMarketingPage).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/opportunities-rewards"
      });
    });
  });
});
