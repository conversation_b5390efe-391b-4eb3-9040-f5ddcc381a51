import { CommonPageMapper } from "@src/contentManagement/CommonPageMapper";

describe("CommonPageMapper", () => {
  const microCopies = {
    "common.creatorNetwork": "Creator Network",
    "common.how": "How",
    "common.reward": "Reward",
    "common.perks": "Perks",
    "common.faq": "FAQ",
    "common.faqs": "FAQs",
    "common.messages": "Messages",
    "common.policies": "Policies",
    "common.legal": "Legal",
    "common.disclaimer": "Disclaimer",
    "common.updates": "Updates",
    "common.terms": "Terms",
    "common.privacy": "Privacy",
    "common.rights": "Rights",
    "common.report": "Report",
    "common.home": "Home",
    "common.works": "Works",
    "common.rewards": "Rewards",
    "common.signIn": "Sign In",
    "common.logIn": "Log In",
    "common.apply": "Apply",
    "common.applyNow": "Apply Now",
    "common.requestToJoin": "Request to Join",
    "common.cancel": "Cancel",
    "common.ok": "OK",
    "common.discard": "Discard",
    "common.next": "Next",
    "common.save": "Save",
    "common.edit": "Edit",
    "common.join": "Join",
    "common.back": "Back",
    "common.browse": "Browse",
    "common.yes": "Yes",
    "common.no": "No",
    "common.remove": "Remove",
    "common.pageNotFound": "Page Not Found",
    "common.pageNotFoundContent": "Page Not Found Content",
    "common.unhandledError": "Unhandled Error",
    "common.unhandledErrorMessage": "Unhandled Error Message",
    "common.logout": "Logout",
    "common.notifyMe": "Notify Me",
    "common.emailAddress": "Email Address",
    "common.unauthorized": "Unauthorized",
    "common.dashboard": "Dashboard",
    "common.opportunities": "Opportunities",
    "common.myContent": "My Content",
    "common.about": "About",
    "common.notifications": "Notifications",
    "common.myProfile": "My Profile",
    "common.signout": "Sign Out",
    "common.prev": "Prev",
    "common.submit": "Submit",
    "common.information": "Information",
    "common.creatorType": "Creator Type",
    "common.connect": "Connect",
    "common.send": "Send",
    "common.copyright": "Copyright",
    "common.approved": "Approved",
    "common.rejected": "Rejected",
    "common.approvalNotRequired": "Approval Not Required",
    "common.submitted": "Submitted",
    "common.updated": "Updated",
    "common.pendingApproval": "Pending Approval",
    "common.changesRequired": "Changes Required",
    "common.viewChangesRequired": "View Changes Required",
    "common.hideChangesRequired": "Hide Changes Required",
    "common.inScan": "In Scan",
    "common.video": "Video",
    "common.sentOn": "Sent On",
    "common.additionalDescription": "Additional Description",
    "common.file": "File",
    "common.url": "URL",
    "common.update": "Update",
    "common.from": "From",
    "common.modalConfirmationTitle": "Modal Confirmation Title",
    "common.confirmationDesc1": "Confirmation Description 1",
    "common.confirmationDesc2": "Confirmation Description 2",
    "common.connectedAccounts": "Connected Accounts",
    "common.legalDocuments": "Legal Documents",
    "common.paymentInformation": "Payment Information",
    "common.paymentInfo": "Payment Info",
    "common.creatorCode": "Creator Code",
    "common.declineTermsAndCondition": "Decline",
    "common.contract": "Contract",
    "common.completed": "Completed",
    "common.justNow": "Just Now",
    "common.today": "Today",
    "common.policy": "Policy",
    "common.disclosure": "Disclosure",
    "common.toasts.contentSubmittedTitle": "Content submission successful",
    "common.toasts.contentSubmittedDescription": "You have successfully submitted content for this opportunity!",
    "common.toasts.declineInvitationTitle": "Invitation Declined",
    "common.toasts.declineInvitationDescription": "You’ve declined this opportunity.",
    "common.months.January": "January",
    "common.months.February": "February",
    "common.months.March": "March",
    "common.months.April": "April",
    "common.months.May": "May",
    "common.months.June": "June",
    "common.months.July": "July",
    "common.months.August": "August",
    "common.months.September": "September",
    "common.months.October": "October",
    "common.months.November": "November",
    "common.franchises": "Franchises",
    "common.close": "Close",
    "common.calendar": "Calendar"
  };

  it("maps common page labels", () => {
    const mapper = new CommonPageMapper();
    const labels = mapper.map(microCopies).commonPageLabels;

    expect(labels.creatorNetwork).toEqual("Creator Network");
    expect(labels.how).toEqual("How");
    expect(labels.reward).toEqual("Reward");
    expect(labels.perks).toEqual("Perks");
    expect(labels.faq).toEqual("FAQ");
    expect(labels.faqs).toEqual("FAQs");
    expect(labels.messages).toEqual("Messages");
    expect(labels.policies).toEqual("Policies");
    expect(labels.legal).toEqual("Legal");
    expect(labels.disclaimer).toEqual("Disclaimer");
    expect(labels.updates).toEqual("Updates");
    expect(labels.terms).toEqual("Terms");
    expect(labels.privacy).toEqual("Privacy");
    expect(labels.rights).toEqual("Rights");
    expect(labels.report).toEqual("Report");
    expect(labels.home).toEqual("Home");
    expect(labels.works).toEqual("Works");
    expect(labels.rewards).toEqual("Rewards");
    expect(labels.signIn).toEqual("Sign In");
    expect(labels.logIn).toEqual("Log In");
    expect(labels.apply).toEqual("Apply");
    expect(labels.applyNow).toEqual("Apply Now");
    expect(labels.requestToJoin).toEqual("Request to Join");
    expect(labels.cancel).toEqual("Cancel");
    expect(labels.ok).toEqual("OK");
    expect(labels.discard).toEqual("Discard");
    expect(labels.next).toEqual("Next");
    expect(labels.save).toEqual("Save");
    expect(labels.edit).toEqual("Edit");
    expect(labels.join).toEqual("Join");
    expect(labels.back).toEqual("Back");
    expect(labels.browse).toEqual("Browse");
    expect(labels.yes).toEqual("Yes");
    expect(labels.no).toEqual("No");
    expect(labels.remove).toEqual("Remove");
    expect(labels.pageNotFound).toEqual("Page Not Found");
    expect(labels.pageNotFoundContent).toEqual("Page Not Found Content");
    expect(labels.unhandledError).toEqual("Unhandled Error");
    expect(labels.unhandledErrorMessage).toEqual("Unhandled Error Message");
    expect(labels.logout).toEqual("Logout");
    expect(labels.notifyMe).toEqual("Notify Me");
    expect(labels.emailAddress).toEqual("Email Address");
    expect(labels.unauthorized).toEqual("Unauthorized");
    expect(labels.dashboard).toEqual("Dashboard");
    expect(labels.opportunities).toEqual("Opportunities");
    expect(labels.myContent).toEqual("My Content");
    expect(labels.about).toEqual("About");
    expect(labels.notifications).toEqual("Notifications");
    expect(labels.myProfile).toEqual("My Profile");
    expect(labels.signout).toEqual("Sign Out");
    expect(labels.prev).toEqual("Prev");
    expect(labels.submit).toEqual("Submit");
    expect(labels.information).toEqual("Information");
    expect(labels.creatorType).toEqual("Creator Type");
    expect(labels.connect).toEqual("Connect");
    expect(labels.send).toEqual("Send");
    expect(labels.copyright).toEqual("Copyright");
    expect(labels.approved).toEqual("Approved");
    expect(labels.rejected).toEqual("Rejected");
    expect(labels.approvalNotRequired).toEqual("Approval Not Required");
    expect(labels.submitted).toEqual("Submitted");
    expect(labels.updated).toEqual("Updated");
    expect(labels.pendingApproval).toEqual("Pending Approval");
    expect(labels.changesRequired).toEqual("Changes Required");
    expect(labels.viewChangesRequired).toEqual("View Changes Required");
    expect(labels.hideChangesRequired).toEqual("Hide Changes Required");
    expect(labels.inScan).toEqual("In Scan");
    expect(labels.video).toEqual("Video");
    expect(labels.sentOn).toEqual("Sent On");
    expect(labels.additionalDescription).toEqual("Additional Description");
    expect(labels.file).toEqual("File");
    expect(labels.url).toEqual("URL");
    expect(labels.update).toEqual("Update");
    expect(labels.from).toEqual("From");
    expect(labels.modalConfirmationTitle).toEqual("Modal Confirmation Title");
    expect(labels.confirmationDesc1).toEqual("Confirmation Description 1");
    expect(labels.confirmationDesc2).toEqual("Confirmation Description 2");
    expect(labels.connectedAccounts).toEqual("Connected Accounts");
    expect(labels.legalDocuments).toEqual("Legal Documents");
    expect(labels.paymentInformation).toEqual("Payment Information");
    expect(labels.justNow).toEqual("Just Now");
    expect(labels.today).toEqual("Today");
    expect(labels.policy).toEqual("Policy");
    expect(labels.disclosure).toEqual("Disclosure");
    expect(labels.toasts.contentSubmittedTitle).toEqual("Content submission successful");
    expect(labels.toasts.contentSubmittedDescription).toEqual(
      "You have successfully submitted content for this opportunity!"
    );
    expect(labels.toasts.declineInvitationTitle).toEqual("Invitation Declined");
    expect(labels.toasts.declineInvitationDescription).toEqual("You’ve declined this opportunity.");
    expect(labels.months.January).toEqual("January");
    expect(labels.months.February).toEqual("February");
    expect(labels.months.March).toEqual("March");
    expect(labels.months.April).toEqual("April");
    expect(labels.months.May).toEqual("May");
    expect(labels.months.June).toEqual("June");
    expect(labels.months.July).toEqual("July");
    expect(labels.months.August).toEqual("August");
    expect(labels.months.September).toEqual("September");
    expect(labels.months.October).toEqual("October");
    expect(labels.months.November).toEqual("November");
    expect(labels.franchises).toEqual("Franchises");
    expect(labels.close).toEqual("Close");
    expect(labels.declineTermsAndCondition).toEqual("Decline");
    expect(labels.contract).toEqual("Contract");
    expect(labels.completed).toEqual("Completed");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new CommonPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key common.creatorNetwork is absent");
  });
});
