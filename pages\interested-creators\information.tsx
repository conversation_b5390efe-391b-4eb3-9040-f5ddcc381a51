import "reflect-metadata";
import React, { ComponentType, memo, useCallback, useState } from "react";
import Error from "../_error";
import { useAppContext } from "@src/context";
import MigrationLayout from "../../components/MigrationLayout";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import { CommunicationFormRules, CreatorFormRules } from "../../components/FormRules/CreatorForm";
import flags from "../../utils/feature-flags";
import { useRouter } from "next/router";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorInformationProps from "@src/serverprops/InterestedCreatorInformationProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { ConnectAccountsPageLabels } from "@src/contentManagement/ConnectAccountsPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/contentManagement/CommunicationPreferencesPageMapper";
import { AddContentPageLabels } from "@src/contentManagement/AddContentPageMapper";
import { useDependency } from "@src/context/DependencyContext";
import {
  facebookIcon,
  instagramIcon,
  tiktokIcon,
  twitchIcon,
  useToast,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";

const Information: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/Information"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);
const connectAccounts = [
  {
    value: "facebook",
    accountIcon: facebookIcon,
    redirectUrl: `/api/facebook-login`
  },
  {
    value: "instagram",
    accountIcon: instagramIcon,
    redirectUrl: `/api/instagram-login`
  },
  {
    value: "twitch",
    accountIcon: twitchIcon,
    redirectUrl: `/api/twitch-login`
  },
  {
    value: "tiktok",
    accountIcon: tiktokIcon,
    redirectUrl: `/api/tiktok-login`
  },
  {
    value: "youtube",
    accountIcon: youTubeIcon,
    redirectUrl: `/api/youtube-login`
  }
];

export const YOUTUBE_NO_CHANNEL_ERROR = "save-you-tube-account-unknown-connected-account";
export const INSTA_WARNING_ERROR = "save-instagram-account-unknown-instagram-business-account";
export const INSTA_CANNOT_CONNECT = "save-instagram-account-cannot-connect-account";
export const INSTAGRAM_STEPS_LINK =
  "https://business.instagram.com/getting-started?fbclid=IwAR2RCQ_lweAva29YXvt7Nfa2wDshHe9oT5LjbHXbGpSKzhIs56G5gfQlrUk";

export type Country = {
  value: string;
  label: string;
  name: string;
};
export type Language = {
  value: string;
  label: string;
  name?: string;
};
export type PreferredLanguage = {
  code: string;
  name: string;
};

export type ContentUrl = {
  url: string;
  followers: string;
};
export type ContentUrlWithoutFollowers = {
  url: string;
};
export type Information = {
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  defaultGamerTag: string;
  originEmail: string;
  contentLanguages?: Array<Language>;
  preferredLanguage?: PreferredLanguage;
  contentUrls?: Array<ContentUrl>;
  country?: Country;
  dateOfBirth: string;
  countryCode?: string;
  contentAccounts?: Array<ContentUrl>;
};
type Fbpage = {
  accessToken: string;
  id: string;
  name: string;
};
export type Fbpages = {
  pages: Array<Fbpage>;
};
export type InterestedCreatorInformationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator?: InterestedCreator & Information;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  pages: Array<Fbpage>;
  pageLabels: AddContentPageLabels &
    BreadcrumbPageLabels &
    CommunicationPreferencesPageLabels &
    CommonPageLabels &
    InformationPageLabels &
    ConnectAccountsPageLabels;
  FLAG_COUNTRIES_BY_TYPE: boolean;
};
export type Rules = Partial<CreatorFormRules & CommunicationFormRules>;

export const interestedCreatorPages = {
  information: "Information",
  creatorTypes: "CreatorTypes",
  franchises: "FranchisesYouPlay"
};

export default memo(function InterestedCreatorInformation({
  interestedCreator,
  pages,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}: InterestedCreatorInformationProps) {
  const {
    informationLabels,
    addContentPageLabels,
    communicationPreferencesPageLabels,
    commonPageLabels,
    connectAccountsLabels
  } = pageLabels;
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const [accountToRemove, setAccountToRemove] = useState(null);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);
  const { applicationsClient, metadataClient, errorHandler, analytics } = useDependency();
  const {
    dispatch,
    state,
    state: { exceptionCode = null, sessionUser = null, isLoading = false } = {}
  } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const { locale } = useRouter();
  const { warning, error: errorToast } = useToast();
  const logout = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [router]);

  const layout = {
    main: {
      unhandledError: commonPageLabels.unhandledError
    },
    buttons: {
      close: commonPageLabels.close
    }
  };
  const infoLabels = {
    interestedCreatorTitle: informationLabels?.interestedCreatorTitle,
    messages: {
      ...(informationLabels.messages as Record<never, never>)
    }
  };
  const translation = {
    messages: {
      ...(communicationPreferencesPageLabels?.messages as Record<never, never>)
    }
  };
  const formLabels = {
    ...(communicationPreferencesPageLabels.labels as Record<never, never>),
    ...informationLabels,
    ...(informationLabels.labels as Record<never, never>),
    ...addContentPageLabels,
    ...commonPageLabels,
    connectSocialMediaAccountTitle: connectAccountsLabels.title,
    connectSocialMediaAccountDescription: connectAccountsLabels.description
  };
  const informationPageLabels = {
    ...commonPageLabels,
    ...informationLabels
  };

  const onClose = useCallback(() => {
    setShowConfirmation(!showConfirmation);
  }, [showConfirmation]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={informationLabels.informationPageTitle}
      className="interested-creator"
      onClose={onClose}
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        franchisesYouPlay: commonPageLabels.franchises,
        information: commonPageLabels.information,
        creatorType: commonPageLabels.creatorType
      }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      isLoading={isLoading}
    >
      <Information
        interestedCreator={interestedCreator}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        stableDispatch={stableDispatch}
        state={state}
        labels={{
          layout: layout,
          formLabels: formLabels,
          pageLabels: informationPageLabels,
          translation: translation,
          infoLabels: infoLabels
        }}
        redirectedToNextStepUrl="/interested-creators/creator-types"
        errorHandling={errorHandler}
        configuration={{ metadataClient: metadataClient, applicationsClient: applicationsClient }}
        onClose={onClose}
        setShowConfirmation={setShowConfirmation}
        showConfirmation={showConfirmation}
        router={router}
        locale={locale}
        errorToast={errorToast}
        warning={warning}
        connectAccountLabels={connectAccountsLabels}
        setShowAddConfirmation={setShowAddConfirmation}
        showAddConfirmation={showAddConfirmation}
        accountToRemove={accountToRemove}
        setAccountToRemove={setAccountToRemove}
        showRemoveAccountModal={showRemoveAccountModal}
        setShowRemoveAccountModal={setShowRemoveAccountModal}
        pages={pages}
        connectAccounts={connectAccounts}
        handleCancelRegistration={logout}
      />
    </MigrationLayout>
  );
});

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorInformationProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorInformationProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.information);
  const { pages, accessToken } = (req.session.fbPages || {
    pages: [],
    accessToken: ""
  }) as { pages: string[]; accessToken: string };

  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "information");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      pageLabels,
      pages,
      accessToken,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled()
    }
  };
};
