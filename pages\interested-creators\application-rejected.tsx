import "reflect-metadata";
import React, { ComponentType } from "react";
import flags from "../../utils/feature-flags";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import InterestedCreatorApplicationStatus from "../../src/interestedCreators/InterestedCreatorApplicationStatus";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationRejectedPageProps from "@src/serverprops/ApplicationRejectedPageProps";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { ApplicationRejectedPageLabels } from "@src/contentManagement/ApplicationRejectedPageMapper";
import { useDependency } from "@src/context/DependencyContext";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

const ApplicationRejectedPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/ApplicationRejectedPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type ApplicationRejectedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  locale: string;
  showInitialMessage?: boolean;
  application: InterestedCreatorApplicationStatus;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  pageLabels: ApplicationRejectedPageLabels & CommonPageLabels;
};

export default function Rejected({
  locale,
  application,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}: ApplicationRejectedProps): JSX.Element {
  const { applicationRejectedLabels, commonPageLabels } = pageLabels;
  const { creatorNetwork, close } = commonPageLabels;
  const { configuration, analytics } = useDependency();

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <div className="mg-bg"> </div>
          <ApplicationRejectedPage
            labels={applicationRejectedLabels}
            locale={locale}
            analytics={analytics}
            emailId={application?.email}
            canApply={configuration.FLAG_INTERESTED_CREATOR_CAN_APPLY && application?.canApply}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={application.createdDate as unknown as string}
            reSubmitRequestDate={(application?.canResubmitRequestDate ?? (" " as unknown)) as string}
            redirectedToInformatioForm="/interested-creators/information"
            redirectedToMain="/"
            logoutPath="/api/logout"
            applicationRejectedThumbnail={"/img/home-header--980w-x-690h.png"}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationRejectedPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationRejectedProps>;
  }

  const application = await withCreatorApplication(req, res, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isRejected()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationRejected");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      pageLabels,
      application: { ...application },
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
