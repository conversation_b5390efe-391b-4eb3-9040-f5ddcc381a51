import "reflect-metadata";
import React from "react";
import { render, screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { FranchisesYouPlayPageLabels } from "@src/contentManagement/FranchisesYouPlayPageMapper";
import InterestedCreatorFranchisesYouPlay from "pages/interested-creators/franchises-you-play";
import { mockMatchMedia } from "../../helpers/window";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsFranchisesYouPlay", () => {
  const router = { locale: "en-us", push: jest.fn() };
  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };
  const pageLabels = {
    informationLabels: {},
    franchisesYouPlayPageLabels: {},
    commonPageLabels: {},
    breadcrumbPageLabels: {}
  } as InformationPageLabels & CommonPageLabels & BreadcrumbPageLabels & FranchisesYouPlayPageLabels;
  const interestedCreatorFranchisesYouPlayProps = {
    interestedCreator: initialInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels: pageLabels,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator)
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const analytics = {
    cancelledCreatorApplication: jest.fn()
  };

  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      configuration: { BASE_PATH: "/support-a-creator" },
      metadataClient: {},
      analytics
    });
  });

  it("shows remote franchises you play component", async () => {
    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
