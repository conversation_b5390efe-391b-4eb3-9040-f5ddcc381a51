import { AgeRestrictionPageMapper } from "@src/contentManagement/AgeRestrictionPageMapper";

describe("AgeRestrictionPageMapper", () => {
  const microCopies = {
    "ageRestriction.title": "Age Restriction Title",
    "ageRestriction.bannerImageLabel": "Banner Image Label",
    "ageRestriction.subTitle": "Sub Title",
    "common.close": "Close"
  };

  it("maps age restriction page labels", () => {
    const mapper = new AgeRestrictionPageMapper();
    const labels = mapper.map(microCopies).ageRestrictionLabels;

    expect(labels.title).toEqual("Age Restriction Title");
    expect(labels.bannerImageLabel).toEqual("Banner Image Label");
    expect(labels.subTitle).toEqual("Sub Title");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new AgeRestrictionPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key ageRestriction.title is absent");
  });
});
