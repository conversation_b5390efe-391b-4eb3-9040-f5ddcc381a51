import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import Information from "./../../pages/onboarding/information";
import { aCreatorWithPayableStatus } from "../factories/creators/CreatorWithPayableStatus";
import { aPrimaryPlatform, aSecondaryPlatform } from "../factories/platforms/PreferredPlatform";
import { aUser } from "../factories/User/User";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../helpers/window";
import { aLocalizedDate } from "../factories/LocalizedDateBuilder";
import CreatorsService, { CreatorWithPayableStatusProfile } from "../../src/api/services/CreatorsService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../src/context/DependencyContext";
import { aCountry, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import Random from "../factories/Random";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../src/context/DependencyContext");
jest.mock("../../src/api/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../src/api/services/CreatorsService"),
    register: jest.fn(),
    getCreatorWithPayableStatus: jest.fn()
  };
});

describe("Information", () => {
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY");
  const informationProps = {
    user: aUser({ status: "ACTIVE" })
  };
  const router = {
    push: jest.fn(),
    locale: "en-us",
    pathname: "/onboarding/information"
  };
  const platforms = [aPlatform()];
  const countries = [aCountry(), aCountry()];
  const metadataService = {
    getCountries: jest.fn().mockResolvedValue(countries),
    getPlatformsMatching: jest.fn().mockResolvedValue(platforms)
  };
  mockMatchMedia();
  const errorHandler = jest.fn();
  const analytics = {
    startedOnboardingFlow: jest.fn(),
    confirmedPlatform: jest.fn(),
    canceledOnboardingFlow: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      analytics,
      errorHandler,
      metadataClient: {},
      creatorsClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "creator_network",
        FLAG_PER_PROGRAM_PROFILE: false,
        DEFAULT_AVATAR_IMAGE: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png"
      }
    });
    MetadataService.mockReturnValue(metadataService);
  });

  it("shows migrated creator information", async () => {
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        accountInformation: { dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years) }
      })
    );
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });

    renderPage(<Information {...informationProps} />);

    expect(await screen.findByRole("heading", { name: /information:title/i })).toBeInTheDocument();
    expect(await screen.findByText(/information:description/i)).toBeInTheDocument();
    expect(await screen.findByText(/information:infoTitle/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue(creator.accountInformation.firstName)).toBeInTheDocument();
    expect(screen.getByDisplayValue(creator.accountInformation.lastName)).toBeInTheDocument();
    expect(screen.getByText(creator.accountInformation.defaultGamerTag)).toBeInTheDocument();
    expect(screen.getByText(creator.accountInformation.originEmail)).toBeInTheDocument();
    expect(screen.getByDisplayValue(dateOfBirthAfter18years)).toBeInTheDocument();
    await waitFor(() => {
      expect(analytics.startedOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.startedOnboardingFlow).toHaveBeenCalledWith({ locale: "en-us" });
    });
  });

  it("shows future creator information for new users", async () => {
    const futureCreator = {
      email: "<EMAIL>",
      dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years)
    };

    renderPage(<Information {...informationProps} futureCreator={futureCreator} />);

    await waitFor(() => {
      expect(screen.getByLabelText(/^information:labels.firstName/i)).toHaveValue("");
      expect(screen.getByLabelText(/^information:labels.lastName/i)).toHaveValue("");
      expect(screen.getByText(informationProps.user.username)).toBeInTheDocument();
      expect(screen.getByText(futureCreator.email)).toBeInTheDocument();
      expect(screen.getByDisplayValue(dateOfBirthAfter18years)).toBeInTheDocument();
    });
  });

  it("registers a new creator", async () => {
    const creatorPrimaryPlatform = aPlatform({ value: "xbox", label: "xbox" });
    const platforms = [creatorPrimaryPlatform];
    const creatorCountry = aCountry({ label: "Canada" });
    const countries = [creatorCountry, aCountry({ label: "India" })];
    const futureCreator = {
      email: "<EMAIL>",
      dateOfBirth: aLocalizedDate().minusYears(18).build().millisecondsEpoch,
      nucleusId: 123456
    };
    const payload = {
      information: {
        firstName: "Jane",
        lastName: "Doe",
        dateOfBirth: dateOfBirthAfter18years,
        country: creatorCountry,
        street: "Main St. 123",
        city: "Hyderabad",
        state: "Telangana",
        zipCode: "72000",
        primaryPlatform: creatorPrimaryPlatform,
        secondaryPlatforms: [],
        nucleusId: futureCreator.nucleusId,
        originEmail: futureCreator.email
      }
    };

    metadataService.getCountries = jest.fn().mockResolvedValue(countries);
    metadataService.getPlatformsMatching = jest.fn().mockResolvedValue(platforms);
    CreatorsService.register.mockImplementation(() => Promise.resolve());
    renderPage(<Information {...informationProps} futureCreator={futureCreator} />);
    await waitFor(() => {
      const firstName = screen.getByLabelText(/^information:labels.firstName/i);
      expect(firstName.getAttribute("value")).toBe("");
    });
    await userEvent.type(screen.getByLabelText(/^information:labels.firstName/i), payload.information.firstName);
    await userEvent.type(screen.getByLabelText(/^information:labels.lastName/i), payload.information.lastName);
    await userEvent.type(screen.getByLabelText(/^information:labels.street/i), payload.information.street);
    await userEvent.type(screen.getByLabelText(/^information:labels.city/i), payload.information.city);
    await userEvent.type(screen.getByLabelText(/^information:labels.state/i), payload.information.state);
    await userEvent.type(screen.getByLabelText(/^information:labels.zipCode/i), payload.information.zipCode);
    expect(screen.getByLabelText(/country/i)).toHaveTextContent("Canada");
    expect(screen.getByLabelText("xbox")).toBeInTheDocument;

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(CreatorsService.register).toHaveBeenCalledTimes(1);
      payload.information.dateOfBirth = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
      expect(CreatorsService.register).toHaveBeenCalledWith(payload);
    });
  });

  it("shows modal with logout option", async () => {
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        accountInformation: { dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years) }
      })
    );
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    renderPage(<Information {...informationProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        accountInformation: { dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years) }
      })
    );
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });

    renderPage(<Information {...informationProps} />);
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        page: "/onboarding/information",
        locale: "en-us"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Confirmed Platform' event when clicking on 'Next' button", async () => {
    const primaryPlatform = "Nintendo Switch";
    const secondaryPlatforms = ["PS4", "XBox"];
    const creator = aCreatorWithPayableStatus({
      accountInformation: { dateOfBirth: aLocalizedDate().minusYears(18).build().millisecondsEpoch },
      preferredPlatforms: [
        aPrimaryPlatform({ label: primaryPlatform, name: primaryPlatform }),
        aSecondaryPlatform({ label: secondaryPlatforms[0], name: secondaryPlatforms[0] }),
        aSecondaryPlatform({ label: secondaryPlatforms[1], name: secondaryPlatforms[1] })
      ]
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: new CreatorWithPayableStatusProfile(creator)
    });
    CreatorsService.register.mockResolvedValue({
      data: creator
    });

    renderPage(<Information {...informationProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(analytics.confirmedPlatform).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedPlatform).toHaveBeenCalledWith({
        locale: "en-us",
        primaryPlatform,
        secondaryPlatforms
      });
      expect(router.push).toHaveBeenCalledWith("/franchises-you-play");
    });
  });

  it("hides back button for Personal Information step", async () => {
    const creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        accountInformation: { dateOfBirth: LocalizedDate.epochFromFormattedDate(dateOfBirthAfter18years) }
      })
    );
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });

    renderPage(<Information {...informationProps} />);

    expect(await screen.findByRole("heading", { name: /information:title/i })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: /Back/i })).not.toBeInTheDocument();
  });

  describe("with 'FLAG_COUNTRIES_BY_TYPE' flag enabled", () => {
    it("pre-populates in creator information", async () => {
      const creator = new CreatorWithPayableStatusProfile(
        aCreatorWithPayableStatus({
          mailingAddress: { country: { code: "MX" } }
        })
      );
      const countries = [aCountry(), { value: "MX", label: "Mexico", name: "Mexico" }];
      metadataService.getCountriesMatching = jest.fn().mockResolvedValue(countries);
      CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
        data: creator
      });

      renderPage(<Information {...informationProps} FLAG_COUNTRIES_BY_TYPE />);

      await waitFor(() => {
        expect(metadataService.getCountriesMatching).toHaveBeenCalledTimes(1);
        expect(screen.getByText("Mexico")).toBeInTheDocument();
        expect(metadataService.getCountriesMatching).toHaveBeenCalledWith();
      });
    });
  });

  describe("with 'FLAG_ONBOARDING_CUSTOM_LINKS' flag enabled", () => {
    it("registers a new creator with FLAG_ONBOARDING_CUSTOM_LINKS flag enabled", async () => {
      const creatorPrimaryPlatform = aPlatform({ value: "xbox", label: "xbox" });
      const platforms = [creatorPrimaryPlatform];
      const creatorCountry = aCountry({ label: "Canada" });
      const countries = [creatorCountry, aCountry({ label: "India" })];
      const futureCreator = {
        email: Random.email(),
        dateOfBirth: aLocalizedDate().minusYears(18).build().millisecondsEpoch,
        nucleusId: Random.nucleusId()
      };
      const payload = {
        information: {
          firstName: Random.firstName(),
          lastName: Random.lastName(),
          dateOfBirth: dateOfBirthAfter18years,
          country: creatorCountry,
          street: Random.streetAddress(),
          city: Random.city(),
          state: Random.state(),
          zipCode: Random.zipCode(),
          primaryPlatform: creatorPrimaryPlatform,
          secondaryPlatforms: [],
          nucleusId: futureCreator.nucleusId,
          originEmail: futureCreator.email
        }
      };
      const { information } = payload;

      metadataService.getCountries = jest.fn().mockResolvedValue(countries);
      metadataService.getPlatformsMatching = jest.fn().mockResolvedValue(platforms);
      CreatorsService.register.mockImplementation(() => Promise.resolve());
      renderPage(<Information {...informationProps} futureCreator={futureCreator} FLAG_ONBOARDING_CUSTOM_LINKS />);
      await waitFor(() => {
        const firstName = screen.getByLabelText(/^information:labels.firstName/i);
        expect(firstName.getAttribute("value")).toBe("");
      });
      await userEvent.type(screen.getByLabelText(/^information:labels.firstName/i), payload.information.firstName);
      await userEvent.type(screen.getByLabelText(/^information:labels.lastName/i), payload.information.lastName);
      await userEvent.type(screen.getByLabelText(/^information:labels.street/i), payload.information.street);
      await userEvent.type(screen.getByLabelText(/^information:labels.city/i), payload.information.city);
      await userEvent.type(screen.getByLabelText(/^information:labels.state/i), payload.information.state);
      await userEvent.type(screen.getByLabelText(/^information:labels.zipCode/i), payload.information.zipCode);
      expect(screen.getByLabelText(/country/i)).toHaveTextContent("Canada");
      expect(screen.getByLabelText("xbox")).toBeInTheDocument;

      await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

      await waitFor(() => {
        expect(CreatorsService.register).toHaveBeenCalledTimes(1);
        payload.information.dateOfBirth = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
        expect(CreatorsService.register).toHaveBeenCalledWith({
          ...payload,
          information: { ...information, contentUrls: [{ url: "", followers: "" }] }
        });
      });
    });
  });
});
