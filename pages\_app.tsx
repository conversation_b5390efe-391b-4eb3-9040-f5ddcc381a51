import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/creator-network.css";
import "@eait-playerexp-cn/notifications-ui/dist/styles/index.css";
import "@eait-playerexp-cn/interested-creators-ui/dist/styles/index.css";
import "../styles/globals.css";
import { appWithTranslation, useTranslation } from "next-i18next";
import TagManager from "../components/TagManager";
import { AppWrapper } from "@src/context";
import { withToastProvider } from "../components/toast";
import Favicon from "../components/icons/Favicon/Favicon";
import BuildInfo from "../components/BuildInfo";
import InformationMessageModal from "../components/pages/InformationMessageModal";
import { useState } from "react";
import labelsCommon from "../config/translations/common";
import SessionService from "../src/api/services/SessionService";
import TraceProvider from "../components/TraceProvider";
import { AppProps } from "next/app";
import { DependencyProvider } from "@src/context/DependencyContext";
import { errorHandlerFactory } from "@src/errorHandling/errorHandler";
import { newBrowserClient } from "@eait-playerexp-cn/http-client";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";

function MyApp({ Component, pageProps }: AppProps) {
  const config = pageProps.runtimeConfiguration || {};
  const {
    APP_ENV,
    APP_DEBUG,
    SUPPORTED_LOCALES,
    BUILD_VERSION,
    INITIAL_MESSAGE_TITLE,
    INITIAL_MESSAGE_DESCRIPTION,
    PROGRAM_CODE,
    user
  } = config;
  const [showInformationMessageModal, setInformationMessageModal] = useState(true);
  const { t } = useTranslation(["common"]);
  const labels = labelsCommon(t);
  const onCloseModal = () => {
    SessionService.removeEntry("showInitialMessage");
    setInformationMessageModal(false);
  };
  const errorHandler = errorHandlerFactory(APP_DEBUG, SUPPORTED_LOCALES);
  const analytics = new BrowserAnalytics({ user, program: PROGRAM_CODE });
  const metadataClient = newBrowserClient({
    baseUrl: config.METADATA_API_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const notificationsClient = newBrowserClient({
    baseUrl: config.NOTIFICATIONS_MFE_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const creatorsClient = newBrowserClient({
    baseUrl: config.CREATORS_API_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const applicationsClient = newBrowserClient({
    baseUrl: config.APPLICATIONS_MFE_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });

  return (
    <div className="app-container">
      <Favicon />
      <DependencyProvider
        configuration={config}
        errorHandler={errorHandler}
        metadataClient={metadataClient}
        notificationsClient={notificationsClient}
        creatorsClient={creatorsClient}
        analytics={analytics}
        applicationsClient={applicationsClient}
      >
        <TraceProvider>
          <AppWrapper>
            <TagManager />
            {pageProps.showInitialMessage && showInformationMessageModal ? (
              <InformationMessageModal
                title={INITIAL_MESSAGE_TITLE}
                description={INITIAL_MESSAGE_DESCRIPTION}
                labels={{
                  gotit: labels.buttons.gotit,
                  close: labels.buttons.close,
                  creatorNetwork: labels.header.creatorNetwork
                }}
                close={onCloseModal}
              />
            ) : (
              <Component {...pageProps} />
            )}
            {APP_ENV !== "prod" && <BuildInfo version={BUILD_VERSION} />}
          </AppWrapper>
        </TraceProvider>
      </DependencyProvider>
    </div>
  );
}

export default withToastProvider(appWithTranslation(MyApp));
