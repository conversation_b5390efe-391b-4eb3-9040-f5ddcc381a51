import { CommunicationPreferencesPageMapper } from "@src/contentManagement/CommunicationPreferencesPageMapper";

describe("CommunicationPreferencesPageMapper", () => {
  const microCopies = {
    "communicationPreferences.title": "Title",
    "communicationPreferences.profileTitle": "Profile Title",
    "communicationPreferences.description": "Description",
    "communicationPreferences.success.updatedInformationHeader": "Updated Information Header",
    "communicationPreferences.success.preferredEmail": "Preferred Email",
    "communicationPreferences.success.preferredPhoneNumber": "Preferred Phone Number",
    "communicationPreferences.success.preferredLanguage": "Preferred Language",
    "communicationPreferences.success.contentLanguage": "Content Language",
    "communicationPreferences.messages.preferredEmail": "Preferred Email",
    "communicationPreferences.messages.preferredEmailTooLong": "Preferred Email Too Long",
    "communicationPreferences.messages.preferredEmailInvalid": "Preferred Email Invalid",
    "communicationPreferences.messages.preferredPhoneNumber": "Preferred Phone Number",
    "communicationPreferences.messages.preferredPhoneNumberTooLong": "Preferred Phone Number Too Long",
    "communicationPreferences.messages.contentLanguage": "Content Language",
    "communicationPreferences.messages.language": "Language",
    "communicationPreferences.labels.addDiscord": "Add Discord",
    "communicationPreferences.labels.discordTitle": "Discord Title",
    "communicationPreferences.labels.discordDescription": "Discord Description",
    "communicationPreferences.labels.preferredEmailAddressTitle": "Preferred Email Address Title",
    "communicationPreferences.labels.preferredEmailAddressDescription": "Preferred Email Address Description",
    "communicationPreferences.labels.preferredEmail": "Preferred Email",
    "communicationPreferences.labels.preferredPhoneNumberTitle": "Preferred Phone Number Title",
    "communicationPreferences.labels.preferredPhoneNumber": "Preferred Phone Number",
    "communicationPreferences.labels.contentLanguagesTitle": "Content Languages Title",
    "communicationPreferences.labels.contentLanguagesDescription": "Content Languages Description",
    "communicationPreferences.labels.contentLanguage": "Content Language",
    "communicationPreferences.labels.languageTitle": "Language Title",
    "communicationPreferences.labels.languageDescription": "Language Description",
    "communicationPreferences.labels.language": "Language"
  };

  it("maps communication preferences page labels", () => {
    const mapper = new CommunicationPreferencesPageMapper();
    const labels = mapper.map(microCopies).communicationPreferencesPageLabels;

    expect(labels.title).toEqual("Title");
    expect(labels.profileTitle).toEqual("Profile Title");
    expect(labels.description).toEqual("Description");
    expect(labels.success.updatedInformationHeader).toEqual("Updated Information Header");
    expect(labels.success.preferredEmail).toEqual("Preferred Email");
    expect(labels.success.preferredPhoneNumber).toEqual("Preferred Phone Number");
    expect(labels.success.preferredLanguage).toEqual("Preferred Language");
    expect(labels.success.contentLanguage).toEqual("Content Language");
    expect(labels.messages.preferredEmail).toEqual("Preferred Email");
    expect(labels.messages.preferredEmailTooLong).toEqual("Preferred Email Too Long");
    expect(labels.messages.preferredEmailInvalid).toEqual("Preferred Email Invalid");
    expect(labels.messages.preferredPhoneNumber).toEqual("Preferred Phone Number");
    expect(labels.messages.preferredPhoneNumberTooLong).toEqual("Preferred Phone Number Too Long");
    expect(labels.messages.contentLanguage).toEqual("Content Language");
    expect(labels.messages.language).toEqual("Language");
    expect(labels.labels.addDiscord).toEqual("Add Discord");
    expect(labels.labels.discordTitle).toEqual("Discord Title");
    expect(labels.labels.discordDescription).toEqual("Discord Description");
    expect(labels.labels.preferredEmailAddressTitle).toEqual("Preferred Email Address Title");
    expect(labels.labels.preferredEmailAddressDescription).toEqual("Preferred Email Address Description");
    expect(labels.labels.preferredEmail).toEqual("Preferred Email");
    expect(labels.labels.preferredPhoneNumberTitle).toEqual("Preferred Phone Number Title");
    expect(labels.labels.preferredPhoneNumber).toEqual("Preferred Phone Number");
    expect(labels.labels.contentLanguagesTitle).toEqual("Content Languages Title");
    expect(labels.labels.contentLanguagesDescription).toEqual("Content Languages Description");
    expect(labels.labels.contentLanguage).toEqual("Content Language");
    expect(labels.labels.languageTitle).toEqual("Language Title");
    expect(labels.labels.languageDescription).toEqual("Language Description");
    expect(labels.labels.language).toEqual("Language");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new CommunicationPreferencesPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key communicationPreferences.title is absent");
  });
});
