import "reflect-metadata";
import { waitFor } from "@testing-library/react";
import React from "react";
import TrustAndSafetyGuidelines from "../../pages/trust-and-safety-guidelines";
import { aUser } from "../factories/User/User";
import { mockMatchMedia } from "../helpers/window";
import { renderPage } from "../helpers/page";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../src/context/DependencyContext");

describe("TrustAndSafetyGuidelines", () => {
  mockMatchMedia();
  const analytics = { viewedMarketingPage: jest.fn() };
  (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us", pathname: "/trust-and-safety-guidelines" }));

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
  });

  it("doesn't log 'Viewed Marketing Page' on loading this page when user is not logged in", async () => {
    renderPage(<TrustAndSafetyGuidelines user={null} interestedCreator={true} />);

    await waitFor(() => expect(analytics.viewedMarketingPage).not.toHaveBeenCalled());
  });

  it("logs 'Viewed Marketing Page' on loading this page", async () => {
    renderPage(<TrustAndSafetyGuidelines user={aUser({ status: "ACTIVE" })} interestedCreator={true} />);

    await waitFor(() => {
      expect(analytics.viewedMarketingPage).toHaveBeenCalledTimes(1);
      expect(analytics.viewedMarketingPage).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/trust-and-safety-guidelines"
      });
    });
  });
});
