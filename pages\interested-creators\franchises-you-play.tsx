import "reflect-metadata";
import React, { ComponentType, FC, useCallback, useState } from "react";
import MigrationLayout from "../../components/MigrationLayout";
import { useAppContext } from "@src/context";
import { useRouter } from "next/router";
import { useToast } from "../../components/toast";
import Error from "../_error";
import flags from "../../utils/feature-flags";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator as InterestedCreatorWithFranchises } from "../../src/api/services/InterestedCreatorsServices";
import { interestedCreatorPages } from "./information";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorFranchisesYouPlayProps from "@src/serverprops/InterestedCreatorFranchisesYouPlayProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { FranchisesYouPlayPageLabels } from "@src/contentManagement/FranchisesYouPlayPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { useDependency } from "@src/context/DependencyContext";

const FranchisesYouPlay: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/FranchisesYouPlay"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type LayoutButtons = {
  yes: string;
  no: string;
  cancel: string;
  next: string;
  submit: string;
  close: string;
};
export type FranchiseMessages = {
  primaryFranchise: string;
};
export type FranchiseLabels = {
  primaryFranchise: string;
  loadMore: string;
};

export type InterestedCreatorFranchisesYouPlayProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InterestedCreatorWithFranchises;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  pageLabels: InformationPageLabels & FranchisesYouPlayPageLabels & CommonPageLabels & BreadcrumbPageLabels;
};

const InterestedCreatorFranchisesYouPlay: FC<InterestedCreatorFranchisesYouPlayProps> = ({
  interestedCreator,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}) => {
  const { franchisesYouPlayPageLabels, commonPageLabels } = pageLabels;
  const { applicationsClient, errorHandler, configuration: config, metadataClient, analytics } = useDependency();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const {
    dispatch,
    state,
    state: { exceptionCode = null, sessionUser = null, isLoading = false } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const router = useRouter();
  const { error: errorToast } = useToast();

  const onGoBack = () => router.push("/interested-creators/creator-types");
  const logout = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [router]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={franchisesYouPlayPageLabels.franchisesYouPlayPageTitle}
      className="interested-creator"
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        franchisesYouPlay: commonPageLabels.franchises,
        information: commonPageLabels.information,
        creatorType: commonPageLabels.creatorType
      }}
      onClose={onClose}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      onGoBack={onGoBack}
      isLoading={isLoading}
    >
      <FranchisesYouPlay
        interestedCreator={interestedCreator}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        labels={{
          franchisesYouPlay: {
            ...franchisesYouPlayPageLabels,
            ...commonPageLabels
          },
          franchisesYouPlayFormLabels: {
            ...franchisesYouPlayPageLabels
          },
          layout: {
            buttons: {
              ...commonPageLabels
            },
            main: {
              unhandledError: commonPageLabels.unhandledError
            }
          }
        }}
        redirectedToNextStepUrl="/interested-creators/complete"
        stableDispatch={stableDispatch}
        state={state}
        errorHandling={errorHandler}
        onClose={onClose}
        setShowConfirmation={setShowConfirmation}
        showConfirmation={showConfirmation}
        configuration={{
          metadataClient: metadataClient,
          applicationsClient: applicationsClient,
          supportedFranchises: config.SUPPORTED_FRANCHISES,
          programCode: config.PROGRAM_CODE
        }}
        router={router}
        locale={router.locale}
        errorToast={errorToast}
        franchisesYouPlayFallbackImages={{
          primaryFranchisefallbackImage: "/img/franchises-you-play/franchise-unselected.png",
          secondaryFranchisefallbackImage: "/img/franchises-you-play/sec-franchise-unselected.png"
        }}
        handleCancelRegistration={logout}
      />
    </MigrationLayout>
  );
};

export default InterestedCreatorFranchisesYouPlay;

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorFranchisesYouPlayProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorFranchisesYouPlayProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.franchises);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }

  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "franchisesYouPlay");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      pageLabels,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
