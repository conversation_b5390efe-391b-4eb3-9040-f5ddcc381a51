import { CreatorTypePageMapper } from "@src/contentManagement/CreatorTypePageMapper";

describe("CreatorTypePageMapper", () => {
  const microCopies = {
    "creatorType.title": "Creator Type Title",
    "creatorType.infoTitle": "Info Title",
    "creatorType.modalContent": "Modal Content",
    "creatorType.interestedCreatorTitle": "Interested Creator Title",
    "creatorType.interestedCreatorDescription": "Interested Creator Description",
    "creatorType.messages.creatorTypes": "Creator Types Message",
    "creatorType.success.updatedInformationHeader": "Updated Information Header",
    "creatorType.success.creatorType": "Success Creator Type",
    "creatorType.labels.youtuber": "Youtuber",
    "creatorType.labels.vlogger": "Vlogger",
    "creatorType.labels.photographer": "Photographer",
    "creatorType.labels.designer_artist": "Designer/Artist",
    "creatorType.labels.blogger": "Blogger",
    "creatorType.labels.liveStreamer": "Live Streamer",
    "creatorType.labels.podcaster": "Podcaster",
    "creatorType.labels.cosplayer": "Cosplayer",
    "creatorType.labels.animator": "Animator",
    "creatorType.labels.screenshoter": "Screenshoter",
    "creatorType.labels.lifestyle": "Lifestyle",
    "creatorType.labels.other": "Other",
    "creatorType.creatorTypePageTitle": "SaC - Creator Type"
  };

  it("maps creator type page labels", () => {
    const mapper = new CreatorTypePageMapper();
    const labels = mapper.map(microCopies).creatorTypePageLabels;

    expect(labels.title).toEqual("Creator Type Title");
    expect(labels.infoTitle).toEqual("Info Title");
    expect(labels.modalContent).toEqual("Modal Content");
    expect(labels.interestedCreatorTitle).toEqual("Interested Creator Title");
    expect(labels.interestedCreatorDescription).toEqual("Interested Creator Description");
    expect(labels.messages.creatorTypes).toEqual("Creator Types Message");
    expect(labels.success.updatedInformationHeader).toEqual("Updated Information Header");
    expect(labels.success.creatorType).toEqual("Success Creator Type");
    expect(labels.labels.youtuber).toEqual("Youtuber");
    expect(labels.labels.vlogger).toEqual("Vlogger");
    expect(labels.labels.photographer).toEqual("Photographer");
    expect(labels.labels.designer_artist).toEqual("Designer/Artist");
    expect(labels.labels.blogger).toEqual("Blogger");
    expect(labels.labels.live_streamer).toEqual("Live Streamer");
    expect(labels.labels.podcaster).toEqual("Podcaster");
    expect(labels.labels.cosplayer).toEqual("Cosplayer");
    expect(labels.labels.animator).toEqual("Animator");
    expect(labels.labels.screenshoter).toEqual("Screenshoter");
    expect(labels.labels.lifestyle).toEqual("Lifestyle");
    expect(labels.labels.other).toEqual("Other");
    expect(labels.creatorTypePageTitle).toEqual("SaC - Creator Type");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new CreatorTypePageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key creatorType.title is absent");
  });
});
