import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import Opportunities from "../../../pages/opportunities";
import {
  anOpportunityWithActivationWindow,
  aOpportunitiesWithoutSearch
} from "../../factories/opportunities/OpportunityWithPerks";
import userEvent from "@testing-library/user-event";
import { aParticipationStatusWithSubmissionStatus } from "../../factories/opportunities/ParticipationStatusWithSubmissionStatus";
import { renderPage } from "../../helpers/page";
import { useRouter } from "next/router";
import { mockMatchMedia } from "../../helpers/window";
import { aParticipationDetails } from "../../factories/opportunities/ParticipationDetails";
import { aCreatorCode } from "../../factories/opportunities/CreatorCode";
import { useAppContext } from "../../../src/context";
import { aContentSubmission } from "../../factories/opportunities/OpportunityContentSubmission";
import { anOpportunityEvent } from "../../factories/opportunities/OpportunityEvent";
import OperationsService from "../../../src/api/services/OperationsService";
import OpportunityService, {
  OpportunityParticipantDetails,
  OpportunityWithActivationWindow
} from "../../../src/api/services/OpportunityService";
import "next/config";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../../src/context/DependencyContext";
import { aPerk, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/context", () => ({
  ...jest.requireActual("../../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: { isLoading: false } })
}));
jest.mock("../../../src/api/services/OpportunityService", () => {
  return {
    ...jest.requireActual("../../../src/api/services/OpportunityService"),
    getParticipationStatusWithSubmissionInformation: jest.fn(),
    searchOpportunitiesWithEventDetails: jest.fn(),
    getParticipationDetails: jest.fn()
  };
});
jest.mock("../../../src/api/services/OperationsService");

describe("Opportunities Page", () => {
  mockMatchMedia();
  let initialOpportunities;
  let initialOpportunitiesParticipationStatus;
  const router = {
    locale: "en-us",
    push: jest.fn(),
    isReady: true
  };
  const opportunityProps = {
    OPPORTUNITY_WITH_PERKS: true,
    user: {}
  };
  const perks = [
    aPerk({ name: "Paid", code: "PAID" }),
    aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
    aPerk({ name: "Collab", code: "COLLAB" }),
    aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
  ];
  const metadataService = {
    getPerks: jest.fn().mockResolvedValue(perks),
    getPlatformsMatching: jest.fn().mockResolvedValue([aPlatform({ label: "XBOX" }), aPlatform({ label: "PC" })])
  };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    const participationId = "OPPO123";
    initialOpportunitiesParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "983", participationId, status: "JOINED" }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" }),
      aParticipationStatusWithSubmissionStatus({ id: "940", status: "DECLINED", participationId: null })
    ];
    initialOpportunities = aOpportunitiesWithoutSearch({
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa123",
            perks: [
              { name: "Paid", code: "PAID" },
              { name: "Collab", code: "COLLAB" }
            ]
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "983",
            perks: [
              { name: "Collab", code: "COLLAB" },
              { name: "VIP Event", code: "VIP_EVENT" }
            ],
            title: "Simple Joined opp #983",
            hasDeliverables: true
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "939",
            title: "Simple open opp",
            perks: [{ name: "VIP Event", code: "VIP_EVENT" }],
            hasGameCodes: false
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "940",
            title: "Simple opp declined",
            perks: [{ name: "Design Council", code: "DESIGN_COUNCIL" }],
            description: "Simple declined description"
          })
        )
      ],
      count: 4,
      total: 4
    });
    useDependency.mockReturnValue({
      analytics: {},
      metadataClient: {},
      errorHandler,
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
    MetadataService.mockReturnValue(metadataService);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: initialOpportunities
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: initialOpportunitiesParticipationStatus
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({ data: [] });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [] }
    });
  });

  it("filters opportunities on enter and updates opportunities list", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" })
    ];
    const opportunitiesResponse = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa 123",
          perks: [
            { name: "Paid", code: "PAID" },
            { name: "Collab", code: "COLLAB" }
          ]
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "939",
          title: "fifa 345",
          perks: [{ name: "VIP Event", code: "VIP_EVENT" }],
          hasGameCodes: false
        })
      )
    ];
    const opportunitiesPage = {
      opportunities: opportunitiesResponse,
      count: 2,
      total: 2
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });
    const { container } = renderPage(<Opportunities {...opportunityProps} />);
    // wait for the initial set of opportunities to be shown
    await waitFor(async () => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    // Look for the search input
    const searchContainer = container.querySelector(".cn-search-box");
    const { getByPlaceholderText } = within(searchContainer);
    const searchInput = getByPlaceholderText("opportunities:searchForOpportunites");
    expect(searchContainer).toBeInTheDocument();
    expect(searchInput).toBeInTheDocument();

    await userEvent.type(searchInput, "fifa{enter}");

    // Check that the opportunities list was filtered
    await waitFor(async () => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(2);
    });
  });

  it("filters opportunities on enter when spaces are added before are after and updates opportunities list", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" })
    ];
    const opportunitiesResponse = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa 123",
          perks: [
            { name: "Paid", code: "PAID" },
            { name: "Collab", code: "COLLAB" }
          ]
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "939",
          title: "fifa 345",
          perks: [{ name: "VIP Event", code: "VIP_EVENT" }],
          hasGameCodes: false
        })
      )
    ];
    const opportunitiesPage = {
      opportunities: opportunitiesResponse,
      count: 2,
      total: 2
    };

    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    const { container } = renderPage(<Opportunities {...opportunityProps} />);
    // wait for the initial set of opportunities to be shown
    await waitFor(async () => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    // Look for the search input
    const searchContainer = container.querySelector(".cn-search-box");
    const { getByPlaceholderText } = within(searchContainer);
    const searchInput = getByPlaceholderText("opportunities:searchForOpportunites");
    expect(searchContainer).toBeInTheDocument();
    expect(searchInput).toBeInTheDocument();

    await userEvent.type(searchInput, "  fifa{enter}    ");

    // Check that the opportunities list was filtered
    await waitFor(async () => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(2);
    });
  });

  it("doesn't use the opportunity title to search if only spaces were typed", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" })
    ];
    const opportunitiesResponse = [
      anOpportunityWithActivationWindow({
        id: "123",
        title: "fifa 123",
        perks: [
          { name: "Paid", code: "PAID" },
          { name: "Collab", code: "COLLAB" }
        ]
      }),
      anOpportunityWithActivationWindow({
        id: "939",
        title: "fifa 345",
        perks: [{ name: "VIP Event", code: "VIP_EVENT" }],
        hasGameCodes: false
      })
    ];
    const opportunitiesPage = {
      opportunities: opportunitiesResponse,
      count: 2,
      total: 2
    };

    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    const { container } = renderPage(<Opportunities {...opportunityProps} />);
    // wait for the initial set of opportunities to be shown
    await waitFor(async () => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    // Look for the search input
    const searchContainer = container.querySelector(".cn-search-box");
    const { getByPlaceholderText } = within(searchContainer);
    const searchInput = getByPlaceholderText("opportunities:searchForOpportunites");
    expect(searchContainer).toBeInTheDocument();
    expect(searchInput).toBeInTheDocument();

    await userEvent.type(searchInput, "  ");

    // Check that the opportunities list was filtered
    await waitFor(async () => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
  });

  it("filters opportunities by perks and updates opportunities list", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunitiesPage = {
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa 123",
            perks: [{ name: "Paid", code: "PAID" }]
          })
        )
      ],
      count: 1,
      total: 1
    };

    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    // Wait for initial opportunities to load
    await waitFor(() => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    const perksFilterButton = screen.getByRole("button", { name: "opportunities:perks" });
    await userEvent.click(perksFilterButton);
    // Wait for perks filters to be shown
    let paidPerkFilter, applyFiltersButton;
    await waitFor(async () => {
      paidPerkFilter = await screen.findByLabelText(/Paid/i);
      expect(paidPerkFilter).toBeInTheDocument();
      applyFiltersButton = await screen.findByText(/opportunities:apply/i);
      expect(applyFiltersButton).toBeInTheDocument();
    });
    await userEvent.click(paidPerkFilter);

    await userEvent.click(applyFiltersButton);

    // Wait for the filtered search results to appear
    await waitFor(() => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(1);
    });
  });

  it("it does not run a new search if no perk filters are selected", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunitiesPage = {
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa 123",
            perks: [{ name: "Paid", code: "PAID" }]
          })
        )
      ],
      count: 1,
      total: 1
    };

    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    // Wait for initial opportunities to load
    await waitFor(() => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    const perksFilterButton = screen.getByRole("button", { name: "opportunities:perks" });

    await userEvent.click(perksFilterButton);

    // Wait for perks filters to be shown
    let paidPerkFilter, applyFiltersButton;
    await waitFor(async () => {
      applyFiltersButton = await screen.findByText(/opportunities:apply/i);
      expect(applyFiltersButton).toBeInTheDocument();
    });
    await userEvent.click(paidPerkFilter);

    await userEvent.click(applyFiltersButton);

    // since no api called occur, mock has only 1 api called which was called initially.
    await waitFor(() => {
      expect(OpportunityService.searchOpportunitiesWithEventDetails).toHaveBeenCalledTimes(1);
    });
  });

  it("it does not run a new search if perk filters did not change", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunitiesPage = {
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa 123",
            perks: [{ name: "Paid", code: "PAID" }]
          })
        )
      ],
      count: 1,
      total: 1
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    // Wait for initial opportunities to load
    await waitFor(() => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    const perksFilterButton = screen.getByRole("button", { name: "opportunities:perks" });

    await userEvent.click(perksFilterButton);

    // Wait for perks filters to be shown
    let paidPerkFilter, applyFiltersButton;
    await waitFor(async () => {
      paidPerkFilter = await screen.findByLabelText(/Paid/i);
      expect(paidPerkFilter).toBeInTheDocument();
      applyFiltersButton = await screen.findByText(/opportunities:apply/i);
      expect(applyFiltersButton).toBeInTheDocument();
    });
    await userEvent.click(paidPerkFilter);

    await userEvent.click(applyFiltersButton);

    // there are 2 api calls total, one initially and one after click on apply button.
    await waitFor(() => {
      expect(OpportunityService.searchOpportunitiesWithEventDetails).toHaveBeenCalledTimes(2);
    });

    await userEvent.click(perksFilterButton);

    await userEvent.click(applyFiltersButton);

    await waitFor(() => {
      expect(OpportunityService.searchOpportunitiesWithEventDetails).toHaveBeenCalledTimes(2);
    });
  });

  it("removes filter when clicking on remove perk chip", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" })
    ];
    const opportunitiesResponse = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa 123",
          perks: [{ name: "Paid", code: "PAID" }]
        })
      ),
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "939",
          title: "fifa 345",
          perks: [{ name: "Paid", code: "PAID" }]
        })
      )
    ];
    const opportunitiesPage = {
      opportunities: opportunitiesResponse,
      count: 2,
      total: 2
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (
        JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 }) ||
        JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10, title: null, perks: [] })
      ) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    // Wait for initial opportunities to load
    await waitFor(() => {
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
    await userEvent.click(screen.getByRole("button", { name: "opportunities:perks" }));
    // Wait for perks filters to be shown
    let paidPerkFilter, applyFiltersButton;
    await waitFor(async () => {
      paidPerkFilter = await screen.findByLabelText(/Paid/i);
      expect(paidPerkFilter).toBeInTheDocument();
      applyFiltersButton = await screen.findByText(/opportunities:apply/i);
      expect(applyFiltersButton).toBeInTheDocument();
    });
    await userEvent.click(paidPerkFilter);
    await userEvent.click(applyFiltersButton);
    // Wait for the filtered search results to appear
    await waitFor(() => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(2);
    });

    await userEvent.click(screen.getByTestId("opportunities-selected-perks-item-remove-icon"));

    // Wait for the initial search results to appear
    await waitFor(() => {
      expect(screen.queryByTestId("loading-icon")).not.toBeInTheDocument();
      const opportunityList = screen.getByTestId("opportunities-list");
      expect(opportunityList.childNodes).toHaveLength(4);
    });
  });

  it("shows opportunities without perks", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunitiesPage = {
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa 123",
            perks: [{ name: "Paid", code: "PAID" }]
          })
        )
      ],
      count: 1,
      total: 1
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: initialOpportunities
        });
      } else {
        return Promise.resolve({
          data: opportunitiesPage
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: participationStatuses
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByTestId("opportunities-list")).toBeInTheDocument();
    });
  });

  it("shows opportunity card v2 for 'Invited' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/dashboard:invited/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /fifa123/i })).toHaveLength(2);
    });
  });

  it("shows opportunity card v2 for 'Open' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/dashboard:open/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple open opp/i })).toHaveLength(2);
    });
  });

  it("navigates to opportunity details page when clicked on a opportunity card", async () => {
    renderPage(<Opportunities {...opportunityProps} />);
    const opportunityButton = (await screen.findAllByRole("button", { name: /fifa123/i }))[0];

    await userEvent.click(opportunityButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/123"));
  });

  it("shows new page of all opportunities when clicking on page number", async () => {
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc1", title: "fifa1" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc2", title: "fifa2" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc3", title: "fifa3" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc4", title: "fifa4" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc5", title: "fifa5" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc6", title: "fifa6" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc7", title: "fifa7" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc8", title: "fifa8" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc9", title: "fifa9" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc10", title: "fifa10" }))
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc11", title: "fifa11" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc12", title: "fifa12" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc13", title: "fifa13" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc14", title: "fifa14" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc15", title: "fifa15" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc16", title: "fifa16" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc17", title: "fifa17" }))
    ];
    const opportunitiesResponse1 = {
      count: 10,
      total: 17,
      opportunities: opportunityPage1
    };
    const opportunitiesResponse2 = {
      count: 10,
      total: 17,
      opportunities: opportunityPage2
    };

    OpportunityService.searchOpportunitiesWithEventDetails.mockImplementation((criteria) => {
      if (JSON.stringify(criteria) !== JSON.stringify({ page: 1, size: 10 })) {
        return Promise.resolve({
          data: opportunitiesResponse1
        });
      } else {
        return Promise.resolve({
          data: opportunitiesResponse2
        });
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (
        JSON.stringify(opportunitiesId) ===
        JSON.stringify(["abc1", "abc2", "abc3", "abc4", "abc5", "abc6", "abc7", "abc8", "abc9", "abc10"])
      ) {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: "abc1", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc2", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc3", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc4", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc5", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc6", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc7", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc8", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc9", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc10", status: "INVITED", participationId: null })
          ]
        });
      } else if (
        JSON.stringify(opportunitiesId) ===
        JSON.stringify(["abc11", "abc12", "abc13", "abc14", "abc15", "abc16", "abc17"])
      ) {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: "abc11", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc12", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc13", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc14", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc15", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc16", status: "INVITED", participationId: null }),
            aParticipationStatusWithSubmissionStatus({ id: "abc17", status: "INVITED", participationId: null })
          ]
        });
      } else {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(OpportunityService.searchOpportunitiesWithEventDetails).toHaveBeenCalledTimes(2);
      expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(1);
      expect(secondPage).toHaveClass("pagination-text-selected");
      expect(screen.getAllByRole("button", { name: "fifa11" })).toHaveLength(2);
    });
  });

  it("shows all perks for an opportunity when user clicks more button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: opportunitiesResponse
    });

    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: opportunityParticipationStatus
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      const { getByRole, getAllByRole } = within(modalDialog);
      expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
      expect(getAllByRole("listitem").length).toBe(opportunity[0].perks.length);
    });
  });

  it("closes the more perks modal when user clicks close button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: opportunitiesResponse
    });
    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: opportunityParticipationStatus
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });
    await userEvent.click(moreButton);
    const closeButton = (await screen.findAllByRole("button", { name: /close$/ }))[0];

    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /allPerks/ })).not.toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /close$/ })).not.toBeInTheDocument();
    });
  });

  it("shows opportunity card v2 for 'Declined' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/opportunities:declined/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple opp declined/i })).toHaveLength(2);
      expect(screen.getByText(/opportunities:designCouncil/i)).toBeInTheDocument();
      expect(screen.getByText("Simple declined description")).toBeInTheDocument();
      expect(screen.queryByText("Registration Window")).not.toBeInTheDocument();
    });
  });

  it("shows all perks for a 'Declined' opportunity when user clicks more button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: opportunitiesResponse
    });
    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "DECLINED", participationId: null })
    ];
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: opportunityParticipationStatus
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      const { getByRole, getAllByRole } = within(modalDialog);
      expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
      expect(getAllByRole("listitem").length).toBe(opportunity[0].perks.length);
    });
  });

  it("shows opportunity card v2 for 'Joined' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/opportunities:joined/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple Joined opp #983/i })).toHaveLength(2);
      expect(screen.getByRole("button", { name: /opportunities:contentSubmission/i })).toBeInTheDocument();
      expect(screen.queryByText("Collab")).not.toBeInTheDocument();
      expect(screen.queryByText("VIP Event")).not.toBeInTheDocument();
    });
  });

  it("navigates to 'Deliverables' tab, for which deliverables is enabled when clicked on a 'Content Submission' button", async () => {
    renderPage(<Opportunities {...opportunityProps} />);
    const contentSubmissionButton = await screen.findByRole("button", { name: /opportunities:contentSubmission/i });

    await userEvent.click(contentSubmissionButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/983?tab=content-deliverables"));
  });

  it("shows creator code details modal for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = "SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] },
        platformDetails: []
      }
    });
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined SAC Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: false,
              perks,
              id: supportACreatorOpportunityId,
              creatorCodeActivationWindow,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED"
            })
          ]
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:creatorCode.title/i })).toBeInTheDocument();
      expect(getByTestId("creator-code-window-details")).toBeInTheDocument();
      expect(getByText(/Apex Legends/i)).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("gets the creator code details for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = " SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      new OpportunityParticipantDetails(
        aParticipationDetails({
          creatorCode: aCreatorCode({
            code: "TESTCREATORCODE340",
            activationWindow: creatorCodeActivationWindow,
            status: "JOINED"
          })
        })
      )
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: null, platformDetails: [] }
    });
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined SAC Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: false,
              perks,
              id: supportACreatorOpportunityId,
              creatorCodeActivationWindow,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED"
            })
          ]
        });
      }
    });
    OpportunityService.getParticipationDetails.mockResolvedValue({
      data: participationDetailsResponse
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(OpportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(1);
      expect(OpportunityService.searchOpportunitiesWithEventDetails).toHaveBeenCalledTimes(1);
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledWith(supportACreatorOpportunityId);
    });
  });

  it("shows error page when getting a creator code details throws an error", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = " SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    metadataService.getPerks = jest.fn().mockResolvedValue(perks);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { creatorCodeDetails: null, platformDetails: [] }
    });
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined SAC Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: false,
              perks,
              id: supportACreatorOpportunityId,
              creatorCodeActivationWindow,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED"
            })
          ]
        });
      }
    });
    OpportunityService.getParticipationDetails.mockRejectedValue(new Error("Failed to get participation details"));
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(OpportunityService.getParticipationDetails).toHaveBeenCalledWith(supportACreatorOpportunityId);
      expect(errorHandler).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("hides the creator code details modal for a joined opportunity when user clicks close button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = " SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] },
        platformDetails: []
      }
    });
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined SAC Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: false,
              perks,
              id: supportACreatorOpportunityId,
              creatorCodeActivationWindow,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationDetails.mockResolvedValue({
      data: participationDetailsResponse
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED"
            })
          ]
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });
    await userEvent.click(creatorCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:creatorCode.title/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("creator-code-window-details")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows game code details modal for a joined opportunity when user clicks 'Game Code' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Game code Opportunity",
              hasDeliverables: false,
              hasGameCodes: true,
              hasEvent: false,
              id: gameCodeOpportunityId,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "CLAIMED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });

    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });

    await userEvent.click(gameCodeButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:getGameCode/i })).toBeInTheDocument();
      expect(getByTestId("content-submission-game-code-wrapper")).toBeInTheDocument();
      expect(getByText(/Apex Legends/i)).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("hides game code details modal for a joined opportunity when user clicks close button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Game code Opportunity",
              hasDeliverables: false,
              hasGameCodes: true,
              hasEvent: false,
              id: gameCodeOpportunityId,
              creatorCodeActivationWindow,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.resolve());
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:getGameCode/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-submission-game-code-wrapper")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows active status in 'Game Code' button for an opportunity with game codes assigned", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Game code Opportunity",
              hasDeliverables: false,
              hasGameCodes: true,
              hasEvent: false,
              id: gameCodeOpportunityId,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-game_code")).toBeInTheDocument();
  });

  it("hides active status in 'Game Code' button for an opportunity if the creator claims the game code", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Game code Opportunity",
              hasDeliverables: false,
              hasGameCodes: true,
              hasEvent: false,
              id: gameCodeOpportunityId,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.resolve());
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(gameCodeButton).not.toHaveProperty("data-testid", "quick-navigation-active-button-game_code");
      expect(screen.getByRole("button", { name: /opportunities:gameCode/ })).toBeInTheDocument();
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
    });
  });

  it("shows active status in 'Content Submission' button for an opportunity, where community manager has requested changes", async () => {
    const perks = [
      aPerk({ name: "Paid", code: "PAID" }),
      aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
      aPerk({ name: "Collab", code: "COLLAB" }),
      aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
    ];
    metadataService.getPerks = jest.fn().mockResolvedValue(perks);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              id: "222",
              perks,
              title: "Simple Joined opp with content submission",
              hasDeliverables: true,
              hasDeliverables: true,
              hasGameCodes: false
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED",
              hasChangesRequested: true
            })
          ]
        });
      }
    });

    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeContentSubmission: { ["222"]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows active status in 'Content Submission' button for an opportunity, where creator hasn't submitted content & window hasn't passed", async () => {
    const perks = [
      aPerk({ name: "Paid", code: "PAID" }),
      aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
      aPerk({ name: "Collab", code: "COLLAB" }),
      aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
    ];
    MetadataService.mockReturnValue({
      getPerks: jest.fn().mockResolvedValue(perks)
    });
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              id: "222",
              perks,
              title: "Simple Joined opp with content submission",
              hasDeliverables: true,
              contentSubmission: aContentSubmission({
                submissionWindow: {
                  end: LocalizedDate.epochMinusDays(4)
                }
              }),
              hasDeliverables: true,
              hasGameCodes: false
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({
              id: "222",
              participationId: "a0YK0000004yUXdgfdfM",
              status: "JOINED",
              hasChangesRequested: true
            })
          ]
        });
      }
    });

    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeContentSubmission: { ["222"]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows error page when claiming a game code throws an error", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Joined Game code Opportunity",
              hasDeliverables: false,
              hasGameCodes: true,
              hasEvent: false,
              id: gameCodeOpportunityId,
              gameTitle: "Apex Legends"
            })
          )
        ],
        total: 1
      }
    });
    OperationsService.viewAssignedGameCodes.mockResolvedValue({
      data: [
        {
          participationId,
          platformId,
          gameCode: {
            id: "testgameid",
            code: "GAME-CODE-FREE",
            status: "ASSIGNED"
          }
        }
      ]
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    OperationsService.claimGameCode.mockImplementation(() => Promise.reject(new Error("Failed to claim game code")));
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const activeGameCodeButton = await screen.findByTestId("quick-navigation-active-button-game_code");

    await userEvent.click(activeGameCodeButton);

    await waitFor(() => {
      expect(OperationsService.claimGameCode).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows event details modal for a joined opportunity when user clicks 'Event' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const eventOpportunityId = "Event_Opportunity";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Event Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: true,
              id: eventOpportunityId,
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(10),
                  endDate: LocalizedDate.epochPlusDays(10),
                  timeZone: "GMT"
                },
                meetingPassword: "Password",
                type: "Remote Event"
              })
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: eventOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:remote/i })).toBeInTheDocument();
      expect(getByTestId("event-detail-modal-window-details")).toBeInTheDocument();
      expect(getByRole("heading", { name: /opportunities:eventDetails:password/i })).toBeInTheDocument();
      expect(getByText("Password")).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("hides event details modal for a joined opportunity when user clicks 'Event' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const eventOpportunityId = "Event_Opportunity";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    OpportunityService.searchOpportunitiesWithEventDetails.mockResolvedValue({
      data: {
        opportunities: [
          new OpportunityWithActivationWindow(
            anOpportunityWithActivationWindow({
              title: "Test Event Opportunity",
              hasDeliverables: false,
              hasGameCodes: false,
              hasEvent: true,
              id: eventOpportunityId,
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(10),
                  endDate: LocalizedDate.epochPlusDays(10),
                  timeZone: "GMT"
                },
                type: "Physical Presence"
              })
            })
          )
        ],
        total: 1
      }
    });
    OpportunityService.getParticipationStatusWithSubmissionInformation.mockImplementation((opportunitiesId) => {
      if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
        return Promise.resolve({
          data: initialOpportunitiesParticipationStatus
        });
      } else {
        return Promise.resolve({
          data: [
            aParticipationStatusWithSubmissionStatus({ id: eventOpportunityId, participationId, status: "JOINED" })
          ]
        });
      }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:event/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:remote/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("event-detail-modal-window-details")).not.toBeInTheDocument();
    });
  });
});
