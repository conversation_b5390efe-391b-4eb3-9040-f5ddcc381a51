import { BreadcrumbPageMapper } from "@src/contentManagement/BreadcrumbPageMapper";

describe("BreadcrumbPageMapper", () => {
  const microCopies = {
    "breadcrumb.modalTitle": "Modal Title",
    "breadcrumb.communicationPreferences": "Communication Preferences",
    "breadcrumb.termsAndConditions": "Terms and Conditions",
    "breadcrumb.connectAccounts": "Connect Accounts",
    "breadcrumb.franchisesYouPlay": "Franchises You Play",
    "breadcrumb.modalMessage": "Modal Message"
  };

  it("maps breadcrumb page labels", () => {
    const mapper = new BreadcrumbPageMapper();
    const labels = mapper.map(microCopies).breadcrumbPageLabels;

    expect(labels.modalTitle).toEqual("Modal Title");
    expect(labels.communicationPreferences).toEqual("Communication Preferences");
    expect(labels.termsAndConditions).toEqual("Terms and Conditions");
    expect(labels.connectAccounts).toEqual("Connect Accounts");
    expect(labels.franchisesYouPlay).toEqual("Franchises You Play");
    expect(labels.modalMessage).toEqual("Modal Message");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new BreadcrumbPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key breadcrumb.modalTitle is absent");
  });
});
